import path from 'path';
import protobuf from 'protobufjs';

import {
    DelayService,
    GameMode,
    GameType,
    JobDataHandler,
    JobType,
    logging,
    RoomStartParams,
    UserStatus,
    MttUserData,
    JobData,
} from 'shared';

import cv from './cv';
import { DomainMgr } from './network/DomainMgr';
import pkwGame from './pkwGame';
import pkwRoom from './pkwRoom';
import { ecdhHandler } from './tools/ecdhHandler';
import * as ServerErrorCodes from '../commoms/SeverErrorCodeTable.json';
import { TableData } from './lobby';
import { workerData } from 'node:worker_threads';

interface WorkerData {
    id: string;
    name: JobType;
    data: JobData;
}

export class PkwMain {
    private static _loginData;

    public static async init(
        currencies: string[],
        onMessage: JobDataHandler,
        proxyUrl?: string,
        gameId?: number,
        gameMode: GameMode = GameMode.NORMAL,
        gameTypeCode: GameType = GameType.NLHE,
        profileName?: string,
    ) {
        cv.initCV(proxyUrl);
        pkwRoom.init();

        const gameModeCode = GameMode.NORMAL; // For all gameModes, including SPLASH, ZOOM and BOMB
        pkwGame.init({
            onMessage,
            gameModeCode,
            delayService: new DelayService(gameMode),
            profileName,
        });
        await this._loadProto();

        pkwRoom.setCurrencies(currencies);
        pkwRoom.setGameId(gameId);
        pkwRoom.setGameTypeCode(gameTypeCode);
        pkwRoom.setLobbyDataCb((tables: TableData[]) => {
            tables.forEach((table) => {
                table.appId = (workerData as WorkerData).data.appId;
            });
            onMessage({ tables });
        });
        onMessage({ status: UserStatus.default });
    }

    public static addDomain(r) {
        DomainMgr.getInstance().addDomain(r.pkwAuthData);
        this._loginData = r;
    }

    public static onWPKLogin(
        loginData: any,
        jobType: JobType,
        joinRoomId: number,
        roomParams: RoomStartParams,
        errorCB = (_: any) => {},
        onLogin?: (data: MttUserData) => void,
    ) {
        logging.withTag('SDK').info('onWPKLogin');

        this._setLoginUserData(loginData);
        cv.dataHandler.getUserData().deviceInfo = `{"disroot":false,"dmodel":"","dname":"wefans","duuid":"${loginData.deviceId}","dversion":""}`;
        this._md5token();
        this._setDomainData();

        pkwRoom.setLoginServerCB((data: MttUserData) => {
            logging.info('PkwMain.onWPKLogin: onLogin', data);
            onLogin?.(data);
        });
        pkwRoom.setPkwUserData(loginData.pkwAuthData.uid, jobType, joinRoomId, roomParams);
        pkwRoom.setErrorCb((error: any) => {
            if (error instanceof Error) {
                errorCB(error);
            } else {
                errorCB(new Error(ServerErrorCodes['ServerErrorCode' + error]?.['-value'] ?? error));
            }
        });

        ecdhHandler.getInstance().ecdh_init();
        cv.netWorkManager.startGame();
    }

    public static async finishGame() {
        return new Promise<void>((resolve) => {
            pkwRoom.leaveRoom(resolve);
        });
    }

    public static closeWebSocket() {
        logging.withTag('WEBSOCKET').info('PKW - Deliberately closing websocket connection...');

        cv.netWork.disconnect();
    }

    /**
     * 加载pb
     */
    private static async _loadProto(): Promise<void> {
        // 设置"pb"字段保持原样输出
        const pb_parse: any = protobuf.parse;
        pb_parse.defaults.keepCase = true;

        // 加载pb协议文件
        const vPBInfo: any[] = [
            { path: path.dirname(__dirname) + '/proto/ws_protocol.proto', type: 'worldPB' },
            { path: path.dirname(__dirname) + '/proto/gs_protocol.proto', type: 'gamePB' },
            { path: path.dirname(__dirname) + '/proto/gate.proto', type: 'gate' },
            { path: path.dirname(__dirname) + '/proto/data.proto', type: 'data' },
        ];

        const loadPromises = vPBInfo.map(async (info) => {
            try {
                const source = await protobuf.load(info.path);
                switch (info.type) {
                    case 'worldPB':
                        cv.worldPB = source;
                        break;
                    case 'gamePB':
                        cv.gamePB = source;
                        break;
                    case 'gate':
                        cv.gatePB = source;
                        break;
                    case 'data':
                        cv.dataPB = source;
                        break;
                    default:
                        break;
                }
            } catch (error: any) {
                logging.error('protobuf load error', error, { path: info.path });
                throw error;
            }
        });
        await Promise.all(loadPromises);
    }

    private static _setDomainData() {
        for (let i = 0; i < this._loginData.pkwAuthData.gate_addr.length; i++) {
            const ite = {
                h5: this._loginData.pkwAuthData.gate_addr[i],
                image_server: this._loginData.pkwAuthData.pkw_file_addr,
                web_server: this._loginData.pkwAuthData.api_addr,
                qiniu2: this._loginData.pkwAuthData.qiniu2,
                wpk: this._loginData.pkwAuthData.avatar_addr,
                wpto: this._loginData.pkwAuthData.wpto,
            };
            cv.domainMgr.addDomain(ite);
        }
    }

    private static _setLoginUserData(loginData: any) {
        const userData = cv.dataHandler.getUserData();

        userData.user_ip = loginData.pkwAuthData.appIP;
        userData.user_token = loginData.pkwAuthData.token;
        userData.u32Uid = loginData.pkwAuthData.uid;
        userData.user_id = loginData.pkwAuthData.uid.toString();
        userData.nick_name = loginData.user.nickname;
        userData.u32Chips = cv.StringTools.times(cv.Number(loginData.amount), 100);
        userData.file_upload_url = loginData.pkwAuthData.pkw_file_addr;
        userData.isallowsimulator = loginData.pkwAuthData.is_allow_simulator;
        userData.isViewWPT = loginData.pkwAuthData.wpt;
        if (loginData.pkwAuthData.client_type) {
            cv.config.SET_CLIENT_TYPE(loginData.pkwAuthData.client_type);
        }

        this._loginData = loginData;
    }

    private static _md5token() {
        const token = cv.dataHandler.getUserData().user_token;
        cv.dataHandler.getUserData().user_token = cv.md5.md5(cv.md5.md5(token));
    }
}
