import cv from '../cv';
import ws_protocol from '../../proto/ws_protocol';
import world_pb = ws_protocol.pb;

export class Tools {
    public static instance: Tools;
    public static getInstance(): Tools {
        if (!this.instance) {
            this.instance = new Tools();
        }
        return this.instance;
    }

    private readonly cardFaceKey = 'user_cardface_type';
    private readonly cardBackKey = 'user_cardback_type';
    private readonly tableBackKey = 'user_tablebg_type';

    private readonly tableBackKey_star = 'user_tablebg_type_star'; // 明星桌背景设置

    private m_eCardFaceType: number = 0;
    private m_eCardBackType: number = 0;
    private m_eTableBackType: number = 0; // 普通桌桌布
    private m_eTableBackType_star: number = -1; // 明星桌桌布

    private _bEnterbackground = false; // 是否进入了后台

    private readonly cardFaceJackfruitKey = 'user_cardface_jackfruit_type';
    private m_eCardFaceJackfruitType: number = 0;

    // 奥马哈牌面设置
    private readonly cardFacePloKey = 'user_cardface_plo_type';
    private m_eCardFacePloType: number = 0;

    public init(): void {
        // 牌背
        const kCardBackValue = this.GetStringByCCFile(this.cardBackKey);
        this.SetCardBack(cv.Number(kCardBackValue || this.m_eCardFaceType));

        // 牌面
        const kCardFaceValue = this.GetStringByCCFile(this.cardFaceKey);
        this.SetCardFace(cv.Number(kCardFaceValue || this.m_eCardBackType));

        // 桌布
        const kTableBackValue = this.GetStringByCCFile(this.tableBackKey);
        this.SetTableBack(cv.Number(kTableBackValue || this.m_eTableBackType));
        // 明星桌桌布
        const kTableBackValue_star = this.GetStringByCCFile(this.tableBackKey_star);
        this.SetTableBack(cv.Number(kTableBackValue_star || this.m_eTableBackType_star), true);

        // 菠萝蜜牌面
        const kCardFaceJackfruitValue = this.GetStringByCCFile(this.cardFaceJackfruitKey);
        this.SetCardFaceJackfruit(cv.Number(kCardFaceJackfruitValue || 0));

        // 奥马哈牌面
        const kCardFacePloValue = this.GetStringByCCFile(this.cardFacePloKey);
        // this.SetCardFacePlo(cv.Number(kCardFacePloValue || 1));
    }

    /**
     * 这是牌面资源类型(这个方法涉及到原始牌资源路径, 很重要)
     * 结合目前引用该方法的逻辑, 此处 eType 数据来源不太可靠, 需要严格筛选
     * @param eType
     */
    public SetCardFace(eType: number) {
        eType = cv.Number(eType);
        eType = Math.floor(eType);
        if (eType >= 0 && eType < cv.Enum.CardFace.CARD_FACE_MAX) {
            const kValue = cv.StringTools.formatC('%d', eType);
            this.SaveStringByCCFile(this.cardFaceKey, kValue);
            this.m_eCardFaceType = eType;
        }
    }

    public static getRoomName(idenID: number, gameID, gameMode, roomMode): string {
        const id = idenID.toString().padStart(4, '0');
        switch (gameID) {
            case cv.Enum.GameId.Texas:
                switch (roomMode) {
                    case world_pb.RoomMode.RoomModeBomb:
                        return gameMode === cv.Enum.CreateGameMode.CreateGame_Mode_Short
                            ? 'HSB' + id
                            : 'HLB' + id;
                    case world_pb.RoomMode.RoomModeLoose:
                        return 'LSHL' + id;
                    default:
                        return gameMode === cv.Enum.CreateGameMode.CreateGame_Mode_Short
                            ? 'HS' + id
                            : 'HL' + id;
                }
            case cv.Enum.GameId.Plo:
                return 'PLO' + id;
            case cv.Enum.GameId.Jackfruit:
                return 'JF' + id;
            case cv.Enum.GameId.ZoomTexas:
            case cv.Enum.GameId.ZoomTexasMax:
                return gameMode === cv.Enum.CreateGameMode.CreateGame_Mode_Short ? 'HSZ' + id : 'HLZ' + id;
            case cv.Enum.GameId.Bet:
                return gameMode === cv.Enum.CreateGameMode.CreateGame_Mode_Short ? 'AS' + id : 'AN' + id;
            default:
                console.error('not matching room name:' + gameID + ' ' + gameMode + ' ' + roomMode);
                return 'No match';
        }
    }

    GetStringByCCFile(kkey: string): string {
        return '';
    }

    public static checkAvatar(avt) {
        return '';
    }

    public SetCardBack(eType: number) {
        eType = cv.Number(eType);
        eType = Math.floor(eType);
        if (eType >= 0 && eType < cv.Enum.CardBack.CARD_BACK_MAX) {
            const kValue = cv.StringTools.formatC('%d', eType);
            //  this.SaveStringByCCFile(this.cardBackKey, kValue);
            this.m_eCardBackType = eType;
        }
    }

    public SetTableBack(eType: number, starRoom: boolean = false) {
        eType = cv.Number(eType);
        eType = Math.floor(eType);
        if (eType >= cv.Enum.TableBack.TABLE_BACK_STAR && eType < cv.Enum.TableBack.TABLE_BACK_MAX) {
            const kValue = cv.StringTools.formatC('%d', eType);
            if (starRoom) {
                //  this.SaveStringByCCFile(this.tableBackKey_star, kValue);  // 明星桌桌布
                this.m_eTableBackType_star = eType;
            } else {
                //  this.SaveStringByCCFile(this.tableBackKey, kValue);
                this.m_eTableBackType = eType;
            }
        }
    }

    SaveStringByCCFile(kkey: string, kValue: string) {}

    public SetCardFaceJackfruit(eType: number) {
        eType = cv.Number(eType);
        eType = Math.floor(eType);
        if (eType >= 0 && eType < cv.Enum.CardFace.CARD_FACE_MAX) {
            const kValue = cv.StringTools.formatC('%d', eType);
            //  this.SaveStringByCCFile(this.cardFaceJackfruitKey, kValue);
            this.m_eCardFaceJackfruitType = eType;
        }
    }

    /**
     * Compare 2 colors if they are same or not
     * @param color1 first color
     * @param color2 second color
     * @param alsoAlpha should colors alpha be compared?
     * @returns true if colors match, false otherwise
     */
    //  public static compareColors(color1: cc.Color, color2: cc.Color, alsoAlpha: boolean = true): boolean {
    //     if (color1.r != color2.r)
    //         return false;
    //     if (color1.g != color2.g)
    //         return false;
    //     if (color1.b != color2.b)
    //         return false;
    //     if (alsoAlpha && color1.a != color2.a)
    //         return false;
    //     return true;
    // }

    public static compareNumbers(nr1: number, nr2: number, decimals: number): number {
        const factor: number = 10 * decimals;
        const _nr1: number = Math.round(nr1 * factor);
        const _nr2: number = Math.round(nr2 * factor);
        if (_nr1 < _nr2) return 1;
        if (_nr2 < _nr1) return -1;
        return 0;
    }
}
