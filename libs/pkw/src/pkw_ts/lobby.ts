import { CurrencyType } from './tools/Enum';
import { pb } from '../proto/ws_protocol';
import { Tools } from './tools/Tools';

export const toTableData = (data: pb.IClubGameSnapshotV3[]): TableData[] => {
    return data.map((d: pb.IClubGameSnapshotV3): TableData => {
        let blinds = [d.small_blind, d.big_blind];
        if (d.straddle) {
            blinds.push(blinds.at(-1) * 2);
        }
        return new TableData(
            d.room_id,
            'NLHE',
            d.game_mode,
            d.room_mode,
            currency(d.currencyType),
            blinds,
            d.straddle,
            d.ante,
            d.player_count,
            d.player_count_max,
            d.left_seatnum,
            Tools.getRoomName(d.iden_num, d.game_id, d.game_mode, d.room_mode),
        );
    });
};

const currency = (currencyType: number | null) => {
    switch (currencyType) {
        case CurrencyType.USD:
            return 'USD';
        case CurrencyType.DIAMOND:
            return 'DIAMOND';
        case CurrencyType.GOLD:
            return 'GOLD';
        default:
            return 'UNKNOWN';
    }
};

export class TableData {
    constructor(
        public tableId: number,
        public gameType: string,
        public gameMode: number,
        public roomMode: number,
        public currency: string,
        public blinds: number[],
        public straddle: boolean,
        public ante: number,
        public playersCount: number,
        public maxPlayers: number,
        public leftSeats: number,
        public tableName: string,
        public appId: number = 0,
    ) {}
}
