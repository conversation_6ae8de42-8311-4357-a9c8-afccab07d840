import cv from '../cv';
import { NetWorkProxy } from './NetWorkProxy';
import gs_protocol, { protocol } from '../../proto/gs_protocol';
import game_pb = gs_protocol.protocol;
import GameDataManager from '../data/GameDataManager';
import { NoticeResetGame, PlayerInfo } from '../data/RoomData';
import { ActionType } from '../tools/Enum';
import INoticeGameInsurance = protocol.INoticeGameInsurance;
import IRequestBuyInsurance = protocol.IRequestBuyInsurance;
import { logging } from 'shared';

export class GameNetWork extends NetWorkProxy {
    public static instance: GameNetWork;

    private _defaultFoldRespCb: Function = null;

    public registerMsg(msgid: number, fn: any): void {
        this.registerMessage(
            msgid,
            (...args) => {
                logging.setLastEventTriggered(game_pb.MSGID[msgid]);
                fn(...args);
            },
            cv.Enum.GameId.Texas,
        );
    }

    public init() {
        // 注册消息
        this.registerMsg(game_pb.MSGID.MsgID_Logon_Response, this.responseLoginServer.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_HeartBeat_Response, this.responseHeartBeat.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_SitDown_Response, this.ResponseSitDown.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_SitDown_Notice, this.NoticeSitDown.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_Buyin_Response, this.ResponseBuyin.bind(this));
        // this.registerMsg(game_pb.MSGID.MsgID_Buyin_To_Owner_Notice, this.NoticeBuyinToOwner.bind(this));
        // this.registerMsg(game_pb.MSGID.MsgID_Buyin_To_Applicant_Notice, this.NoticeBuyinToApplication.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_Buyin_Notice, this.NoticeBuyin.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_Room_Situation_Response, this.ResponseRoomSituation.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_Room_Situation_Notice, this.NoticeRoomSituation.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_JoinRoom_Response, this.JoinRoomResponse.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_Game_Snapshot_Notice, this.NoticeGameSnapShot.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_GameActionTurn_Response, this.ResponseGameActionTurn.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_Standup_Response, this.ResponseStandup.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_Standup_Notice, this.NoticeStandup.bind(this));

        this.registerMsg(game_pb.MSGID.MsgID_ResetGame_Notice, this.NoticeResetGame.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_Game_ElectDealer_Notice, this.NoticeGameElectDealer.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_Game_Blind_Notice, this.NoticeGameBlind.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_Game_HoleCard_Notice, this.NoticeGameHoleCard.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_PlayerActionTurn_Notice, this.NoticePlayerActionTurn.bind(this));
        this.registerMsg(
            game_pb.MSGID.MsgID_BackPosition_Response,
            this.ResponsePlayerBackPosition.bind(this),
        );
        this.registerMsg(game_pb.MSGID.MsgID_BackPosition_Notice, this.NoticePlayerBackPosition.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_PlayerAction_Notice, this.NoticePlayerAction.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_Game_RoundEnd_Notice, this.NoticeGameRoundEnd.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_CommunityCards_Notice, this.NoticeGameCommunityCards.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_Game_Settlement_Notice, this.NoticeGameSettleMent.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_Game_Ante_Notice, this.NoticeGameAnte.bind(this));

        // 保位离桌
        this.registerMsg(game_pb.MSGID.MsgID_StayPosition_Response, this.ResponseStayPosition.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_StayPosition_Notice, this.NoticePlayerStayPosition.bind(this));

        this.registerMsg(
            game_pb.MSGID.MsgID_Waiting_OtherPlayer_Notice,
            this.NoticeWaitingOtherPlayer.bind(this),
        );
        this.registerMsg(game_pb.MSGID.MsgID_PauseGame_Response, this.ResponsePauseGame.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_PauseGame_Notice, this.NoticePauseGame.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_LeaveRoom_Response, this.Response_LeaveRoom.bind(this)); // Leave Zoom Room
        this.registerMsg(game_pb.MSGID.MsgID_QuickLeave_Response, this.Response_QuickLeaveRoom.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_QuickLeave_Notice, this.Response_QuickLeaveNotice.bind(this));

        this.registerMsg(game_pb.MSGID.MsgID_StartGame_Response, this.ResponseStartGame.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_StartGame_Notice, this.NoticeStartGame.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_RealStart_Notice, this.NoticeRealStart.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_DefaultFold_Response, this.ResponseDefaultFold.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_QuickFold_Response, this.ResponseQuickFold.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_LastRound_Win, this.ResponseLastRoundWin.bind(this));

        this.registerMsg(game_pb.MSGID.MsgID_Buyout_Response, this.ResponseBuyout.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_Buyout_Notice, this.NoticeBuyout.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_Action_Response, this.ResponseAction.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_Game_ShowDown_Notice, this.NoticeGameShowDown.bind(this));
        this.registerMsg(
            game_pb.MSGID.MsgID_ForceStandup_Response,
            this.HandleForceStandupResponse.bind(this),
        );
        this.registerMsg(game_pb.MSGID.MsgID_ForceStandup_Notice, this.HandleForceStandupNotice.bind(this));
        this.registerMsg(
            game_pb.MSGID.MsgID_ProhibitSitdown_Response,
            this.HandleProhibitSitdownResponse.bind(this),
        );
        this.registerMsg(
            game_pb.MSGID.MsgID_ProhibitSitdown_Notice,
            this.HandleProhibitSitdownNotice.bind(this),
        );
        this.registerMsg(
            game_pb.MSGID.MsgID_NotiPlayerHoleCard_Notice,
            this.HandleNotiPlayerHoleCardNotice.bind(this),
        );

        // 提前离桌
        this.registerMsg(
            game_pb.MSGID.MsgID_CheckOutAndLeave_Response,
            this.ResponseCheckOutAndLeave.bind(this),
        );
        this.registerMsg(game_pb.MSGID.MsgID_CheckOutAndLeave_Notice, this.NoticeCheckOutAndLeave.bind(this));

        this.registerMsg(game_pb.MSGID.MsgID_ShowCard_Response, this.ResponsePlayerShowCard.bind(this));
        this.registerMsg(game_pb.MSGID.MsgID_ShowCard_Notice, this.NoticePlayerShowCard.bind(this));

        this.registerMsg(game_pb.MSGID.MsgID_Snapshot_Response, this.ResponseSnapshot.bind(this));

        this.registerMsg(
            game_pb.MSGID.MsgID_PlayerBuyinsInfo_Response,
            this.ResponsePlayerBuyinsInfo.bind(this),
        );
        this.registerMsg(game_pb.MSGID.MsgID_PlayerBuyinsInfo_Notice, this.NoticePlayerBuyinsInfo.bind(this));

        this.registerMsg(game_pb.MSGID.MsgId_CriticismStart_Notice, this.NoticeCritisicmStart.bind(this));
        this.registerMsg(
            game_pb.MSGID.MsgId_NotEnoughMoney2Crit_Notice,
            this.NoticeCritisicmNotEnoughMoney.bind(this),
        );

        this.registerMsg(game_pb.MSGID.MsgId_AutoWithdraw_Response, this.ResponseAutoWithdraw.bind(this));

        this.registerMsg(game_pb.MSGID.MsgID_Game_Insurance_Notice, this.NoticeGameInsurance.bind(this));
    }

    public sendGameMsg(
        pbbuf: any,
        msgid: number,
        Roomid: number,
        ServerType: number = 2,
        ServerId: number = 2,
    ): boolean {
        const _curGameID = cv.roomManager.getCurrentGameID(); // cv.GameDataManager.tRoomData.u32GameID;
        if (
            cv.roomManager.currentGameIsZoom() ||
            _curGameID === cv.Enum.GameId.Allin ||
            _curGameID === cv.Enum.GameId.Bet ||
            _curGameID == cv.Enum.GameId.StarSeat ||
            _curGameID == cv.Enum.GameId.Plo
        ) {
            return this.sendMsg(pbbuf, msgid, Roomid, ServerType, _curGameID);
        }

        return this.sendMsg(pbbuf, msgid, Roomid, ServerType, ServerId);
    }

    public NoticeAddActionTime(pbbuf) {
        const msg = this.decodePB('NoticeAddActionTime', pbbuf);
        if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
            cv.MessageCenter.send('add_action_time', msg);
        }
    }

    public RoomNoticeInitiativeDestroy(pbbuf) {
        const msg = this.decodePB('NoticeInitiativeDestroyRoom', pbbuf);
        logging.withTag('GAME_NETWORK').info('[SDK] RoomNoticeInitiativeDestroy', {
            payload: msg,
        });
        if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
            logging.info('InitiativeDestroyRoom');
        } else {
            cv.ToastError(msg.error);
        }
    }

    public NoticeGameShowDown(pbbuf) {
        const msg = this.decodePB('NoticeGameShowDown', pbbuf);
        logging.withTag('GAME_NETWORK').info('[SDK] NoticeGameShowDown', {
            payload: msg,
        });
        if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
            cv.MessageCenter.send('on_game_showdown_noti', msg);
        }
    }

    public ResponseAction(pbbuf) {
        const msg = this.decodePB('ResponseAction', pbbuf);
        // cv.config.logTime("ResponseAction");
        if (msg.error != 0) {
            if (msg.error == 1205) {
                console.log('[Action err] action_fold_error');
                cv.MessageCenter.send('action_fold_error', 1);
            } else if (msg.error == 49) {
                // 下注金额不对
                console.log('[Action err] bad bet amount: ' + JSON.stringify(msg));
                //  cv.MessageCenter.send("revert_control_button_status");
                console.error(msg.error);
                cv.ToastError(msg.error);
            } else {
                console.log('[Action ] ResponseAction error code: ' + msg.error);
                cv.ToastError(msg.error);
            }
        }
    }

    public RequestDefaultFold(u32RoomId, type: ActionType, cb?: Function) {
        const RequestDefaultFold = cv.gamePB.lookupType('RequestDefaultFold');
        const sendGameMsg: object = { roomid: u32RoomId, type };
        const pbbuf = RequestDefaultFold.encode(sendGameMsg).finish();
        this._defaultFoldRespCb = cb;
        this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_DefaultFold_Request, u32RoomId);
    }

    public ResponseDefaultFold(pbbuf) {
        const msg = this.decodePB('ResponseDefaultFold', pbbuf);
        if (msg.Error == 1) {
            // response status is OK
            if (this._defaultFoldRespCb) this._defaultFoldRespCb();
        } else {
            // request default fold failed
        }

        this._defaultFoldRespCb = null;

        // cv.ToastError(msg.Error);
    }

    public RequestQuickFold(u32RoomId: number, isCheckBet: boolean, keepEnd: number) {
        const RequestQuickFold = cv.gamePB.lookupType('RequestQuickFold');
        const sendGameMsg: object = { RoomID: u32RoomId, CheckBet: isCheckBet, keepEnd };
        const pbbuf = RequestQuickFold.encode(sendGameMsg).finish();
        this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_QuickFold_Request, u32RoomId);
    }

    public ResponseQuickFold(pbbuf) {
        const msg = this.decodePB('ResponseQuickFold', pbbuf);
        if (msg.Error != 0) {
            if (msg.Error == 1202) {
                cv.MessageCenter.send('zoom_quickfold_tips');
            } else if (msg.Error == 1205) {
                cv.MessageCenter.send('action_fold_error', 1);
            } else {
                cv.ToastError(msg.Error);
                cv.MessageCenter.send('ShowSportsTipsOnFold');
            }
        }
    }

    public ResponseLastRoundWin(pbbuf) {
        const msg = this.decodePB('NotifyLastRoundWin', pbbuf);
        logging.withTag('GAME_NETWORK').info('[SDK] ResponseLastRoundWin', {
            payload: msg,
        });
        if (!msg) {
            return;
        }
        logging.info('ZoomLastRoundWin');
        cv.MessageCenter.send('showLastRoundWin', cv.StringTools.numToFloatString(msg.amount));
    }

    public NoticeRealStart(pbbuf) {
        const msg = this.decodePB('NoticeRealStart', pbbuf);
        logging.withTag('GAME_NETWORK').info('[SDK] NoticeRealStart', {
            payload: msg,
        });
        if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
            // cv.GameDataManager.tRoomData.u32StartTime = msg.starttime;
            cv.GameDataManager.tRoomData.pkRoomState.isBegin = true;
            cv.MessageCenter.send('notice_real_start', msg);
        }
    }

    public NoticeWaitingOtherPlayer(pbbuf) {
        logging.withTag('GAME_NETWORK').info('[SDK] NoticeWaitingOtherPlayer');
        const msg = this.decodePB('NoticeWaitingOtherPlayer', pbbuf);
        // if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
        //  cv.GameDataManager.tGameData.game_settlement_uuid = "";
        //   cv.MessageCenter.send("on_waiting_other_player");
        // }
    }

    /**
     * 请求主动秀牌
     * @param roomId
     * @param idxs 2张手牌游戏: 类型为数字, 0和1表示第几张牌，如果传2表示两张牌都show; 4张手牌游戏: 类型为数组, 手牌索引数组
     * @param isshow
     * @brief 注: 关于第二个参数"idxs"完全可以用数组代替, 但服务端为了不影响2张手牌游戏逻辑, 单独给4张手牌的新增字段, 这里客户端做个兼容
     */
    public RequestShowCard(roomId: number, idxs: number | number[], isshow: boolean) {
        const msg: game_pb.RequestShowCard = game_pb.RequestShowCard.create();
        msg.roomid = roomId;
        msg.is_show = isshow;

        if (Array.isArray(idxs)) {
            msg.cardList = idxs.slice();
        } else {
            msg.cards = idxs;
        }

        const pbbuf = this.encodePB('RequestShowCard', msg);
        this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_ShowCard_Request, roomId);
    }

    private ResponsePlayerShowCard(pbbuf) {
        const msg = this.decodePB('ResponseShowCard', pbbuf);
        if (msg.roomid === cv.GameDataManager.tRoomData.u32RoomId) {
            console.log(`game_pb.MSGID.MsgID_ShowCard_Response: ${msg.error}`);
        }
    }

    private NoticePlayerShowCard(pbbuf) {
        const msg: game_pb.NoticePlayerShow = this.decodePB('NoticePlayerShow', pbbuf);
        if (msg.roomid === cv.GameDataManager.tRoomData.u32RoomId) {
            cv.MessageCenter.send('player_show_cards', msg);
        }
    }

    public NoticeGameAnte(pbbuf) {
        const msg = this.decodePB('NoticeGameAnte', pbbuf);
        if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
            cv.MessageCenter.send('on_game_anti_noti', msg);
        }
    }

    public NoticeGameSettleMent(pbbuf) {
        const msg: game_pb.NoticeGameSettlement = this.decodePB('NoticeGameSettlement', pbbuf);

        // let msg = this.decodePB("NoticeGameSettlement", pbbuf);
        if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
            let bHad = false;
            for (let i = 0; i < cv.GameDataManager.tRoomData.game_uuids_js.length; i++) {
                if (cv.GameDataManager.tRoomData.game_uuids_js[i] == msg.gameuuid_js) {
                    bHad = true;
                    break;
                }
            }
            if (!bHad && msg.noUseGameuuid != 1) {
                cv.GameDataManager.tRoomData.game_uuids_js.push(msg.gameuuid_js);
                // 如果记录超过限制条数  删除第一条
                const len = cv.GameDataManager.tRoomData.game_uuids_js.length;
                if (msg.hisHands > 0 && len > msg.hisHands) {
                    cv.GameDataManager.tRoomData.game_uuids_js.shift();
                }
            }
            cv.MessageCenter.send('on_game_settlement_noti', msg);
        }
    }

    // on get community cards
    public NoticeGameCommunityCards(pbbuf) {
        const msg = this.decodePB('NoticeCommunityCards', pbbuf);
        if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
            cv.MessageCenter.send('on_game_communitycard_noti', msg);
        }
    }

    public NoticeGameRoundEnd(pbbuf) {
        const msg = this.decodePB('NoticeGameRoundEnd', pbbuf);
        logging.withTag('GAME_NETWORK').info('[SDK] NoticeGameRoundEnd', {
            payload: msg,
        });

        if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
            cv.MessageCenter.send('on_game_endround_noti', msg);
        }
    }

    public NoticePlayerAction(pbbuf) {
        const msg = this.decodePB('NoticePlayerAction', pbbuf);
        if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
            cv.MessageCenter.send('on_game_action_noti', msg);
        }
    }

    public NoticePlayerBackPosition(pbbuf) {
        const msg = this.decodePB('NoticeBackPosition', pbbuf);
        if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
            cv.GameDataManager.tRoomData.updateTablePlayer(msg.player.playerid, msg.player);
            cv.MessageCenter.send('on_back_seat', msg.player.playerid);
        }
    }

    public ResponsePlayerBackPosition(pbbuf) {
        const msg = this.decodePB('ResponseBackPosition', pbbuf);
        logging.withTag('GAME_NETWORK').info('[GAME_NETWORK] ResponsePlayerBackPosition', { payload: msg });
        if (msg.error == 1) {
            cv.MessageCenter.send('onBackSeatSuccess', msg);
        } else if (msg.error == 32) {
            cv.MessageCenter.send('onBackSeatNeedBuyIn', msg);
        } else {
            cv.MessageCenter.send('onBackSeatNoBuyIn', msg);
        }
    }

    public NoticeStartGame(pbbuf) {
        logging.withTag('SDK').info('[SDK] NoticeStartGame');
        const msg: game_pb.NoticeStartGame = this.decodePB('NoticeStartGame', pbbuf);
        if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
            cv.GameDataManager.tRoomData.hasRecvStartGame = true;
            cv.dataHandler.getUserData().totalHands = msg.texasTotalHands;
            cv.MessageCenter.send('on_startgame_noti');
        }
    }

    public ResponseStartGame(pbbuf) {
        const msg = this.decodePB('ResponseStartGame', pbbuf);
    }

    public NoticeResetGame(pbbuf) {
        const msg: NoticeResetGame = this.decodePB('NoticeResetGame', pbbuf);
        cv.MessageCenter.send('on_resetgame_noti', msg);
        // console.log("NoticeResetGame: ");
        // if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
        //     cv.MessageCenter.send("on_resetgame_noti", msg);
        // }
    }

    // bb, sb dealer seatid
    public NoticeGameElectDealer(pbbuf) {
        const msg = this.decodePB('NoticeGameElectDealer', pbbuf);
        cv.MessageCenter.send('on_game_elect_dealer_noti', msg);
        // if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
        //     cv.MessageCenter.send("on_game_elect_dealer_noti", msg);
        // }
    }

    public NoticeGameBlind(pbbuf) {
        const msg = this.decodePB('NoticeGameBlind', pbbuf);
        if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
            cv.MessageCenter.send('on_game_blind_noti', msg);
        }
    }

    public NoticeGameHoleCard(pbbuf) {
        const msg = this.decodePB('NoticeGameHolecard', pbbuf);
        logging.withTag('GAME_NETWORK').info('[SDK] NoticeGameHoleCard', {
            payload: msg,
        });
        if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
            cv.MessageCenter.send('on_game_holecard_noti', msg);
        }
    }

    public NoticePlayerActionTurn(pbbuf) {
        const msg = this.decodePB('NoticePlayerActionTurn', pbbuf);
        if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
            cv.MessageCenter.send('on_game_action_turn_noti', msg);
        }
    }

    public RequestAction(
        u32RoomId: number,
        eType: number,
        u32Amount: number,
        isCheckBet: boolean = false,
        keepEnd: number = 0,
        actionSeq,
    ) {
        logging.withTag('SDK').info(`[SDK] RequestAction - action: ${eType}`);
        const user_token = cv.dataHandler.getUserData().user_token;
        const LoginModule = cv.gamePB.lookupType('RequestAction');
        // u32Amount = cv.StringTools.serverGoldByClient(u32Amount);
        //  cv.config.logTime("RequestAction");
        if (LoginModule) {
            const sendGameMsg: object = {
                roomid: u32RoomId,
                amount: u32Amount,
                action: eType,
                ActionSeq: actionSeq,
                token: user_token,
                keepEnd,
                checkBet: isCheckBet,
            };
            //  console.log(sendGameMsg);
            const pbbuf = LoginModule.encode(sendGameMsg).finish();
            this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_Action_Request, u32RoomId);
            //  cv.GameDataManager.tGameData.m_bIsOnSelfAction = false;
        }
    }

    public RequestBackPosition(u32RoomId: number) {
        logging.withTag('SDK').info('[SDK] RequestBackPosition');
        const sendGameMsg = { roomid: u32RoomId };
        const LoginModule = cv.gamePB.lookupType('RequestBackPosition');
        if (LoginModule) {
            const pbbuf = LoginModule.encode(sendGameMsg).finish();
            this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_BackPosition_Request, u32RoomId);
        }
    }

    public RequestStartGame(u32RoomId: number) {
        logging.withTag('SDK').info('[SDK] RequestStartGame');
        const sendGameMsg = { roomid: u32RoomId };
        const LoginModule = cv.gamePB.lookupType('RequestStartGame');
        if (LoginModule) {
            const pbbuf = LoginModule.encode(sendGameMsg).finish();
            this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_StartGame_Request, u32RoomId);
        }
    }

    public requestLoginServer() {
        logging.withTag('SDK').info('[SDK] requestLoginServer ====> Game websocket was opened.');
        const user_token = cv.dataHandler.getUserData().user_token;

        // hard-coded for now
        const Version = '2.18.0';

        let device_info = cv.dataHandler.getUserData().deviceInfo;
        const client_type = cv.config.GET_CLIENT_TYPE();
        const LoginModule = cv.gamePB.lookupType('RequestLogon');
        if (LoginModule) {
            const sendGameMsg: object = {
                token: user_token,
                version: Version,
                position: this.getPositionInfo(),
                device_info,
                client_type,
            };
            const pbbuf = LoginModule.encode(sendGameMsg).finish();
            this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_Logon_Request, 0);
        }
    }

    public responseLoginServer(puf) {
        logging.withTag('SDK').info('[SDK] responseLoginServer');
        const ResponseGameLogon = cv.gamePB.lookupType('ResponseLogon');
        if (ResponseGameLogon) {
            const buffer = new Uint8Array(puf);
            const data = ResponseGameLogon.decode(buffer);
            const error = data.error;
            logging.withTag('SDK').info('[SDK] responseLoginServer data', { data });
            if (error == 1) {
                if (data.roomid != 0) {
                    // 如果有房间ID说明为重连，需要重新判断是否禁止模拟器入座
                    cv.roomManager.setCurrentRoomID(data.roomid);
                }
                cv.netWorkManager.OnGameServerLogin(error);
            } else {
                cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = false;

                if (error == 226) {
                    // server is in Maintain.
                    cv.MessageCenter.send('sendShowMaintainMsg', true);
                }

                cv.ToastError(error);
            }
        }
    }

    public RequestJoinRoom(roomId: number, gameID: number, isQuick: boolean = false) {
        logging
            .withTag('SDK')
            .info(`Network - RequestJoinRoom - roomId: ${roomId} gameID: ${gameID} isQuick: ${isQuick}`);
        const sendGameMsg = { roomid: roomId, is_quick_sit: isQuick };
        const pbbuf = this.encodePB('RequestJoinRoom', sendGameMsg);

        if (cv.dataHandler.getUserData().m_bIsLoginGameServerSucc) {
            this.sendGameMsg(
                pbbuf,
                game_pb.MSGID.MsgID_JoinRoom_Request,
                roomId,
                cv.Enum.SeverType.SeverType_Game,
                gameID,
            );
        } else {
            this.requestLoginServer();
        }
    }

    public RequestJoinZoomRoom(roomId: number, gameID: number, isQuick: boolean = false) {
        logging.withTag('SDK').info('[SDK] RequestJoinZoomRoom');
        const sendGameMsg = { roomid: roomId, is_quick_sit: isQuick };
        const pbbuf = this.encodePB('RequestJoinRoom', sendGameMsg);

        if (cv.dataHandler.getUserData().m_bIsLoginGameServerSucc) {
            this.sendGameMsg(
                pbbuf,
                game_pb.MSGID.MsgID_JoinRoom_Request,
                roomId,
                cv.Enum.SeverType.SeverType_Game,
                gameID,
            );
        } else {
            this.requestLoginServer();
        }
    }

    public JoinRoomResponse(puf: any) {
        const data = this.decodePB('ResponseJoinRoom', puf);
        logging.withTag('SDK').info(`[SDK] JoinRoomResponse: ${JSON.stringify(data)}`);
        cv.GameDataManager.tRoomData.u32RoomId = data.roomid;
        if (data) {
            cv.roomManager.onJoinRoomResponse(data);
        }
    }

    public NoticeGameSnapShot(puf: any) {
        const result: game_pb.NoticeGameSnapshot = this.decodePB('NoticeGameSnapshot', puf);
        const roomid: number = result.roomid;
        if (roomid === cv.roomManager.getCurrentRoomID()) {
            //   cv.MessageCenter.send("onJoinRoom", result);
        } else {
            console.log(' ============= WARN ================');
            console.log(
                'room id not matched: roomid:' +
                    roomid +
                    ' getCurrentRoomID: ' +
                    cv.roomManager.getCurrentRoomID(),
            );
            console.log(' ============= WARN ================');
        }

        cv.MessageCenter.send('onNoticeGameSnapShot', result);
    }

    // Server ID here is seatID or seat idx
    public RequestSitdown(Roomid: number, severId: number, manually: boolean = false) {
        logging
            .withTag('SDK')
            .info(`[SDK] RequestSitdown. Roomid: ${Roomid} severId: ${severId} manually: ${manually}`);
        // Server ID here is seatID or seat idx
        const SitdownModule = cv.gamePB.lookupType('RequestSitDown');
        if (SitdownModule) {
            const kLocation: any = cv.native.GetLocation();
            const Ip =
                cv.dataHandler.getUserData().user_ip == null
                    ? '127.0.0.1'
                    : cv.dataHandler.getUserData().user_ip;
            const PositionInfo = { latitude: kLocation.latitude, longtitude: kLocation.longtitude, ip: Ip };
            const sendGameMsg = { roomid: Roomid, seatid: severId, position: PositionInfo, isSure: manually };
            const pbbuf = SitdownModule.encode(sendGameMsg).finish();
            this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_SitDown_Request, Roomid); // c++里面没有第3个参数
        }
    }

    public RequestSituation(roomid: number) {
        logging.withTag('SDK').info('[SDK] RequestSituation');
        const Situation = cv.gamePB.lookupType('RequestRoomSituation');
        if (Situation) {
            const sendGameMsg = { roomid };
            const pbbuf = Situation.encode(sendGameMsg).finish();
            this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_Room_Situation_Request, roomid);
        }
    }

    public ResponseSitDown(puf) {
        logging.withTag('SDK').info('[SDK] ResponseSitDown');
        const resp: game_pb.ResponseSitDown = this.decodePB('ResponseSitDown', puf);
        if (resp) {
            logging.withTag('SDK').info(`[SDK] GameNetWork - response: MsgID_SitDown_Response`, resp);

            cv.MessageCenter.send('resp_sit_down_failed', resp);
            switch (resp.error) {
                case 1: {
                    logging.withTag('SDK').info('[SDK] sitDown Done! (no need buyin!)');
                    cv.MessageCenter.send('sitDownDone', false);
                }

                case 22: // room is dismissed
                    logging.withTag('SDK').info('[SDK] the room is dismissed when Sit Down!');
                    cv.MessageCenter.send('roomDismissed');
                    break;

                case 29: // table is full
                    logging.withTag('SDK').info('[SDK] the table is full when Sit Down!');
                    cv.MessageCenter.send('tableFull');
                    break;

                // already sit down
                case 30:
                    logging.withTag('SDK').info('[SDK] the user is already Sit Down!');
                    cv.MessageCenter.send('alreadySitDown');
                    break;

                case 32:
                    {
                        logging.withTag('SDK').info('[SDK]  sitDown need buyin!');
                        cv.MessageCenter.send('needBuyin');
                    }
                    break;

                case 35: //position occupied
                    logging.withTag('SDK').info('[SDK] the seat is Occupied when Sit Down!');
                    cv.MessageCenter.send('seatOccupied', resp);
                    break;

                case 97:
                    {
                        logging.info(
                            cv.StringTools.formatC(
                                'ServerErrorCode97',
                                resp.playername,
                            ),
                        );
                        cv.MessageCenter.send('sitDownUncaughtError', resp);
                    }
                    break;

                case 515:
                    {
                        // Error_Join_Lower_Level_Limit, ServerErrorCode515
                        logging.info('ServerErrorCode' + resp.error);
                        cv.MessageCenter.send('sitDownUncaughtError', resp);
                    }
                    break;

                case 511:
                    {
                        // cv.native.showGpsZeroError();
                        cv.MessageCenter.send('sitDownUncaughtError', resp);
                    }
                    break;

                // verification for real human
                case 1260:
                    {
                        cv.MessageCenter.send('verificationNeeded', resp);
                    }
                    break;

                // 真人验证错误码(x秒后重新认证)
                case 1261:
                    {
                        const strKey: string = 'slider_verify_toast_result_forbid_txt';
                        logging.info(cv.StringTools.formatC(strKey, resp.authVerifyCD));
                        cv.MessageCenter.send('sitDownUncaughtError', resp);
                    }
                    break;

                case 513:
                    {
                        cv.MessageCenter.send('sitDownUncaughtError', resp);
                    }
                    break;
                // 特邀座位 开始//
                case 1254:
                    {
                        cv.MessageCenter.send('sitDownUncaughtError', resp);
                    }
                    break;
                case 1255:
                    cv.MessageCenter.send('sitDownUncaughtError', resp);
                case 1256:
                    {
                        cv.MessageCenter.send('sitDownUncaughtError', resp);
                    }
                    break;
                // 特邀座位 结束//

                case 1301:
                    {
                        const str = cv.StringTools.formatC(
                            'GameScene_sitDownLimit_panel_view_panel_des_2',
                            resp.limit.num,
                            resp.limit.max,
                        );
                        cv.MessageCenter.send('sitDownUncaughtError', resp);
                    }
                    break;

                // Seat reserved for VIP
                case 31121:
                    const str = 'ServerErrorCode' + resp.error;
                    logging.info(str);
                    cv.MessageCenter.send('sitDownUncaughtError', resp);
                    break;

                default:
                    {
                        logging.withTag('SDK').info('[SDK] resp sit down error, err: ' + resp.error);
                        cv.MessageCenter.send('sitDownUncaughtError', resp);
                        cv.ToastError(resp.error);
                    }
                    break;
            }
        }
    }

    public NoticeSitDown(pbbuf) {
        logging.withTag('SDK').info('[SDK] NoticeSitDown');
        const msg = this.decodePB('NoticeSitDown', pbbuf);
        if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
            const player: PlayerInfo = msg.player;
            player.headurl = '';
            cv.GameDataManager.tRoomData.addTablePlayer(player);
            cv.MessageCenter.send('on_sitdown_succ', player.playerid);
        }
    }

    public requestHeartBeat(): boolean {
        const RequestHeartBeat = cv.gamePB.lookupType('RequestHeartBeat');
        if (RequestHeartBeat) {
            const sendGameMsg = {
                uid: cv.dataHandler.getUserData().u32Uid,
                position: this.getPositionInfo(),
            };
            const pbbuf = RequestHeartBeat.encode(sendGameMsg).finish();
            return this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_HeartBeat_Request, 0);
        }
    }

    public responseHeartBeat(puf) {
        const tempModule = cv.gamePB.lookupType('ResponseHeartBeat');
        if (tempModule) {
            const recvMsg = new Uint8Array(puf);
            const result = tempModule.decode(recvMsg);
            if (result != null) {
                const error = result.uid;
                //  console.log("[HeartBeat] on GameServer response: " + JSON.stringify(result));
                // cv.netWorkManager.onGameHeartBeat();
            }
        }
    }

    public getPositionInfo(): any {
        const PositionInfoModule = cv.gamePB.lookupType('PositionInfo');
        if (PositionInfoModule) {
            const tempInfo: any = cv.native.GetLocation();
            const ip = cv.dataHandler.getUserData().user_ip;
            const obj = {
                longtitude: tempInfo.longitude,
                latitude: tempInfo.latitude,
                ip: ip == null ? '127.0.0.1' : ip,
            };
            return obj;
        }
    }

    public RequestBuyin(u32RoomId: number, u32Amount: number): boolean {
        // ownerid: number, clubid: number, allianceid: number
        logging.withTag('SDK').info('[SDK] Req Buyin', { u32RoomId, u32Amount });
        const RequestBuyin = cv.gamePB.lookupType('RequestBuyin');
        if (RequestBuyin) {
            const amount = u32Amount;
            // const amount = cv.StringTools.serverGoldByClient(u32Amount);
            const sendGameMsg = { roomid: u32RoomId, amount }; // ownerid: ownerid, clubid: clubid, allianceid: allianceid
            const pbbuf = RequestBuyin.encode(sendGameMsg).finish();
            return this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_Buyin_Request, u32RoomId);
        }
    }

    public RequestGameActionTurn(roomid: number) {
        logging.withTag('SDK').info('[SDK] RequestGameActionTurn');
        const RequestGameActionTurn = cv.gamePB.lookupType('RequestGameActionTurn');
        if (RequestGameActionTurn) {
            const Token: string = cv.dataHandler.getUserData().user_token;
            if (Token === '') {
                /// Token = g_pkTool->GetStringByCCFile("user_token");
                // if (Token.empty()) return;
                return;
            }
            const sendGameMsg = { roomid, token: Token };
            const pbbuf = RequestGameActionTurn.encode(sendGameMsg).finish();
            this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_GameActionTurn_Request, roomid);
        }
    }

    public ResponseGameActionTurn(puf) {
        logging.withTag('SDK').info('[SDK] ResponseGameActionTurn');
        const ResponseGameActionTurn = cv.gamePB.lookupType('ResponseGameActionTurn');
        if (ResponseGameActionTurn) {
            const buffer = new Uint8Array(puf);
            const data = ResponseGameActionTurn.decode(buffer);
            const error = data.error;
            console.log(data);
            // ToastError(resp.error());
        }
    }

    public ResponseBuyin(puf) {
        logging.withTag('SDK').info('[SDK] ResponseBuyin');
        const ResponseBuyin = cv.gamePB.lookupType('ResponseBuyin');
        if (ResponseBuyin) {
            const recvMsg = new Uint8Array(puf);
            const ret = ResponseBuyin.decode(recvMsg);
            logging.withTag('SDK').info('[SDK] ResponseBuyin data', { payload: ret });
            const error = ret.error;
            const playername = ret.playername;

            if (error == 1) {
                logging.withTag('SDK').info('[SDK] buyin success!');
                cv.MessageCenter.send('onBoughtIn');
                return;
            } else if (error == 39) {
                logging
                    .withTag('SDK')
                    .info(
                        '[SDK] Buyin Failed, The number of gold coins has reached the Maximum Limit. Change Room!',
                    );
            } else if (error == 43) {
                logging.info('ServerErrorCode' + error);
            } else if (error === 97) {
                logging.info(cv.StringTools.formatC('ServerErrorCode' + error, playername));
            } else if (error == 511) {
                //  cv.native.showGpsZeroError();
            } else if (error == 515) {
                // Error_Join_Lower_Level_Limit, ServerErrorCode515
                logging.info('ServerErrorCode' + error);
            } else if (error == 1301) {
                const str = cv.StringTools.formatC(
                    'GameScene_sitDownLimit_panel_view_panel_des_2',
                    ret.limit.num,
                    ret.limit.max,
                );
                cv.MessageCenter.send('sit_down_limit', str);
            } else if (error == 114) {
                const curGameId = cv.roomManager.getCurrentGameID();
                if (
                    curGameId != cv.Enum.GameId.Bet &&
                    !cv.roomManager.currentGameIsZoom() &&
                    curGameId != cv.Enum.GameId.Allin &&
                    cv.GameDataManager.tRoomData.pkRoomParam.game_mode ==
                        cv.Enum.CreateGameMode.CreateGame_Mode_Short
                ) {
                    if (cv.GameDataManager.tRoomData.i32SelfSeat != -1) {
                    }
                } else if (curGameId == cv.Enum.GameId.Plo) {
                    if (cv.GameDataManager.tRoomData.i32SelfSeat != -1) {
                    }
                } else if (curGameId == cv.Enum.GameId.Bet) {
                    if (cv.GameDataManager.tRoomData.i32SelfSeat != -1) {
                    }
                }
            } else {
                cv.ToastError(error);
            }
            cv.MessageCenter.send('onBuyInFailed', error);

            // 发送服务器验证后的带入失败的消息, 用于游戏场景的数据采集
            //cv.MessageCenter.send("buyin_failed_by_server", ret);
        }
    }

    public showGameShop() {
        logging.withTag('SDK').info('[SDK] showGameShop');
        //  cv.SHOP.RechargeClick();
    }

    public gameStandUp() {
        logging.withTag('SDK').info('[SDK] gameStandUp');
        // 容错 只有自己在桌位上才发送请求站立
        const player: PlayerInfo = GameDataManager.tRoomData.GetTablePlayer(
            cv.dataHandler.getUserData().u32Uid,
        );
        if (player != null) {
            cv.gameNet.RequestStandup(GameDataManager.tRoomData.u32RoomId);
        }
    }

    public NoticeBuyin(puf) {
        logging.withTag('SDK').info('[GAME_NETWORK] NoticeBuyin');
        const NoticeBuyin = cv.gamePB.lookupType('NoticeBuyin');
        if (NoticeBuyin) {
            const recvMsg = new Uint8Array(puf);
            const ret = NoticeBuyin.decode(recvMsg);
            cv.MessageCenter.send('onNoticeBuyin', ret);
        }
        return;
    }

    public ResponseModifyBuyinLimit(puf) {
        logging.withTag('SDK').info('[SDK] ResponseModifyBuyinLimit');
        const ResponseModifyBuyinLimit = cv.gamePB.lookupType('ResponseModifyBuyinLimit');
        if (ResponseModifyBuyinLimit) {
            const recvMsg = new Uint8Array(puf);
            const ret = ResponseModifyBuyinLimit.decode(recvMsg);
            const error = ret.error;
            const playername = ret.playername;
            if (error == 97) {
                logging.info('ServerErrorCode' + error + playername);
            } else {
                cv.ToastError(error);
            }
        }
    }

    public NoticeModifyBuyinLimit(puf) {
        logging.withTag('SDK').info('[SDK] NoticeModifyBuyinLimit');
        const NoticeModifyBuyinLimit = cv.gamePB.lookupType('NoticeModifyBuyinLimit');
        if (NoticeModifyBuyinLimit) {
            const recvMsg = new Uint8Array(puf);
            const ret = NoticeModifyBuyinLimit.decode(recvMsg);
            const error = ret.error;
            // let buyin_limit = ret.buyin_limit;
            const buyin_now = ret.buyin_now;
            if (ret.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
                // cv.GameDataManager.tRoomData.u32BuyinLimit = buyin_limit;
                cv.GameDataManager.tRoomData.u32Buyin = buyin_now;
            }
        }
    }

    public ResponseRoomSituation(puf) {
        logging.withTag('SDK').info('[SDK] ResponseRoomSituation');
        const ResponseRoomSituation = cv.gamePB.lookupType('ResponseRoomSituation');
        if (ResponseRoomSituation) {
            const recvMsg = new Uint8Array(puf);
            const ret = ResponseRoomSituation.decode(recvMsg);
            cv.ToastError(ret.error);
        }
    }

    public NoticeRoomSituation(puf) {
        const NoticeRoomSituation = cv.gamePB.lookupType('NoticeRoomSituation');
        if (!NoticeRoomSituation) {
            return;
        }

        const recvMsg = new Uint8Array(puf);
        const situation: gs_protocol.protocol.NoticeRoomSituation = NoticeRoomSituation.decode(recvMsg);
        if (situation.roomid !== cv.GameDataManager.tRoomData.u32RoomId) {
            return;
        }
        logging.withTag('GAME_NETWORK').info('[GAME_NETWORK] NoticeRoomSituation');
        for (const player of situation.buyin_player_list) {
            cv.GameDataManager.tRoomData.updateBuyinInfo(player as gs_protocol.protocol.PlayerBuyinInfo);
        }

        cv.MessageCenter.send('on_room_situation', situation);
    }

    public RequestStandup(roomId: number) {
        logging.withTag('SDK').info('[SDK] RequestStandup');
        const RequestStandup = cv.gamePB.lookupType('RequestStandup');
        if (RequestStandup) {
            const sendGameMsg = { roomid: roomId };
            const pbbuf = RequestStandup.encode(sendGameMsg).finish();
            this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_Standup_Request, roomId);
        }
    }

    public ResponseStandup(puf) {
        const ResponseStandup = cv.gamePB.lookupType('ResponseStandup');
        if (ResponseStandup) {
            const buffer = new Uint8Array(puf);
            const ret = ResponseStandup.decode(buffer);
            if (ret.error == 1255) {
                logging.info('ServerErrorCode1255');
            } else {
                cv.ToastError(ret.error);
            }
        }
    }

    public NoticeStandup(puf) {
        logging.withTag('SDK').info('[SDK] NoticeStandup');
        const NoticeStandup = cv.gamePB.lookupType('NoticeStandup');
        if (NoticeStandup) {
            const buffer = new Uint8Array(puf);
            const ret = NoticeStandup.decode(buffer);
            logging.withTag('SDK').info('[SDK] NoticeStandup data', {
                payload: ret,
                curr_roomid: cv.GameDataManager.tRoomData.u32RoomId,
            });
            if (ret.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
                cv.MessageCenter.send('on_standup_succ', ret.target_uid);
            }
        }
    }

    /**
     * name
     */
    public ResponseSnapshot(pbbuf) {
        logging.withTag('SDK').info('[SDK] ResponseSnapshot');
        const msg = this.decodePB('ResponseSnapshot', pbbuf);
        cv.ToastError(msg.error);
    }

    public RequestSnapshot(roomId: number) {
        logging.withTag('SDK').info('[SDK] RequestSnapshot');
        const sendGameMsg = { roomid: roomId };
        const pbbuf = this.encodePB('RequestSnapshot', sendGameMsg);
        this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_Snapshot_Request, roomId);
    }

    public RequestLeaveRoom(roomId: number) {
        logging.withTag('SDK').info(`[SDK] RequestLeaveRoom roomId: ${roomId}`);
        const sendGameMsg = { roomid: roomId };
        const pbbuf = this.encodePB('RequestLeaveRoom', sendGameMsg);
        this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_LeaveRoom_Request, roomId);
    }

    public Response_LeaveRoom(puf) {
        const data = this.decodePB('ResponseLeaveRoom', puf);
        if (data) {
            logging.withTag('SDK').info('[SDK] Response_LeaveRoom data', { data });
            cv.roomManager.onResponse_LeaveRoom(data);
        } else {
            logging.withTag('SDK').info('[SDK] Response_LeaveRoom data is missing');
        }
    }

    public RequestQuickLeave(roomId: number) {
        logging.withTag('SDK').info('[SDK] RequestQuickLeave');
        const sendGameMsg = { RoomID: roomId };
        const pbbuf = this.encodePB('RequestQuickLeave', sendGameMsg);
        this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_QuickLeave_Request, roomId);
    }

    public Response_QuickLeaveRoom(puf) {
        logging.withTag('SDK').info('[SDK] Response_QuickLeaveRoom');
        const data = this.decodePB('ResponseQuickLeave', puf);
        if (data) {
            logging.withTag('SDK').info('[SDK] Response_QuickLeaveRoom data', { error: data.Error });
            cv.roomManager.onResponse_LeaveRoom(data);
        }
    }

    public Response_QuickLeaveNotice(puf) {
        const data = this.decodePB('NotiQuickLeave', puf);
        logging.withTag('SDK').info('[SDK] Response_QuickLeaveNotice', { data });
        if (data) {
            if (data.PlayerID == cv.dataHandler.getUserData().u32Uid) {
                cv.MessageCenter.send('quick_leave_notice', data);
            }
        }
    }

    public RequestPauseGame(roomId: number, is_Pause: boolean) {
        logging.withTag('SDK').info('[SDK] RequestPauseGame');
        const RequestPauseGame = cv.gamePB.lookupType('RequestPauseGame');
        if (RequestPauseGame) {
            const sendGameMsg = { roomid: roomId, isPause: is_Pause };
            const pbbuf = RequestPauseGame.encode(sendGameMsg).finish();
            this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_PauseGame_Request, roomId);
        }
    }

    public ResponsePauseGame(puf) {
        logging.withTag('SDK').info('[SDK] ResponsePauseGame');
        const ResponsePauseGame = cv.gamePB.lookupType('ResponsePauseGame');
        if (ResponsePauseGame) {
            const buffer = new Uint8Array(puf);
            const ret = ResponsePauseGame.decode(buffer);
            cv.ToastError(ret.error);
        }
    }

    public NoticePauseGame(puf) {
        logging.withTag('SDK').info('[SDK] NoticePauseGame');
        const msg = this.decodePB('NoticePauseGame', puf);
        if (msg) {
            if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
                cv.GameDataManager.tRoomData.pkRoomState.isPause = msg.isPause;
                cv.GameDataManager.tRoomData.pkRoomState.paused = msg.paused;
                if (msg.isPause) {
                    logging.info('PauseGame');
                } else {
                    logging.info('PauseGame2');
                }
            }
            cv.MessageCenter.send('on_PauseGame_succ', msg);
        }
    }

    public RequestStayPosition(roomId: number) {
        logging.withTag('SDK').info('[SDK] RequestStayPosition');
        const RequestStayPosition = cv.gamePB.lookupType('RequestStayPosition');
        if (RequestStayPosition) {
            const sendGameMsg = { roomid: roomId };
            const pbbuf = RequestStayPosition.encode(sendGameMsg).finish();
            this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_StayPosition_Request, roomId);
        }
    }

    public ResponseStayPosition(puf) {
        logging.withTag('SDK').info('[SDK] ResponseStayPosition');
        const ret = this.decodePB('ResponseStayPosition', puf);
        if (ret) {
            if (ret.error == 1) {
                logging.info('ErrorToast34');
            }
        }
    }

    public NoticePlayerStayPosition(pbbuf) {
        const message = this.decodePB('NoticePlayerStay', pbbuf);
        logging.withTag('SDK').info('[SDK] NoticePlayerStayPosition', message);

        if (message.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
            for (let player of message.players) {
                cv.GameDataManager.tRoomData.updateTablePlayer(player.playerid, player);
                cv.MessageCenter.send('onLeaveSeat', player.playerid); // TODO: remove??
            }
        }
    }

    public RequestBuyout(u32RoomId: number, u32Amount: number) {
        logging.withTag('SDK').info('[SDK] RequestBuyout');
        const RequestBuyout = cv.gamePB.lookupType('RequestBuyout');
        if (RequestBuyout) {
            const sendGameMsg = {
                roomid: u32RoomId,
                buyout_gold: u32Amount,
            };
            const pbbuf = RequestBuyout.encode(sendGameMsg).finish();
            return this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_Buyout_Request, u32RoomId);
        }
    }

    public ResponseBuyout(puf) {
        logging.withTag('SDK').info('[SDK] ResponseBuyout');
        const ResponseBuyout = cv.gamePB.lookupType('ResponseBuyout');
        if (ResponseBuyout) {
            const buffer = new Uint8Array(puf);
            const ret = ResponseBuyout.decode(buffer);
            logging.withTag('SDK').info('[SDK] ResponseBuyout, msg: ' + JSON.stringify(ret));
        }
    }

    public NoticeBuyout(puf) {
        logging.withTag('SDK').info('[SDK] NoticeBuyout');
        const NoticeBuyout = cv.gamePB.lookupType('NoticeBuyout');
        if (NoticeBuyout) {
            const buffer = new Uint8Array(puf);
            const ret = NoticeBuyout.decode(buffer);
            logging.withTag('SDK').info('[SDK] NoticeBuyout, msg: ' + JSON.stringify(ret));
        }
    }

    RequestForceStandup(u32RoomId: number, i32TargetID: number) {
        logging.withTag('SDK').info('[SDK] RequestForceStandup');
        const tempMsg = cv.gamePB.lookupType('RequestForceStandup');
        if (tempMsg) {
            const sendGameMsg = { roomid: u32RoomId, targetid: i32TargetID };
            const pbbuf = tempMsg.encode(sendGameMsg).finish();
            this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_ForceStandup_Request, u32RoomId);
        }
    }

    HandleForceStandupResponse(puf: any) {
        const msg = this.decodePB('ResponseForceStandup', puf);
        logging.withTag('SDK').info('[SDK] HandleForceStandupResponse', msg);
    }

    HandleForceStandupNotice(puf: any) {
        const msg = this.decodePB('NoticeForceStandup', puf);
        logging.withTag('SDK').info('[SDK] HandleForceStandupNotice', msg);
    }

    RequestProhibitSitdown(u32RoomId: number, i32TargetID: number, bIsProhibit: boolean) {
        logging.withTag('SDK').info('[SDK] RequestProhibitSitdown');
        const tempMsg = cv.gamePB.lookupType('RequestProhibitSitdown');
        if (tempMsg) {
            const sendGameMsg = { roomid: u32RoomId, targetid: i32TargetID, isProhibitSitdown: bIsProhibit };
            const pbbuf = tempMsg.encode(sendGameMsg).finish();
            this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_ProhibitSitdown_Request, u32RoomId);
        }
    }

    HandleProhibitSitdownResponse(puf: any) {
        logging.withTag('SDK').info('[SDK] HandleProhibitSitdownResponse');
        const msg = this.decodePB('ResponseProhibitSitdown', puf);
        if (msg) {
            cv.ToastError(msg.error);
        }
    }

    HandleProhibitSitdownNotice(puf: any) {
        logging.withTag('SDK').info('[SDK] HandleProhibitSitdownNotice');
        const msg = this.decodePB('NoticeProhibitSitdown', puf);
        if (msg) {
            if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
                cv.GameDataManager.tRoomData.prohibit_sitdown_list = msg.prohibit_sitdown_list;
                cv.MessageCenter.send('update_prohibit_button');
            }
        }
    }

    HandleNotiPlayerHoleCardNotice(puf: any) {
        logging.withTag('GAME_NETWORK').info('[SDK] HandleNotiPlayerHoleCardNotice');
    }

    public RequestCheckOutAndLeave(roomId: number) {
        logging.withTag('SDK').info('[SDK] RequestCheckOutAndLeave');
        const sendGameMsg = { roomid: roomId };
        const pbbuf = this.encodePB('RequestCheckOutAndLeave', sendGameMsg);
        this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_CheckOutAndLeave_Request, roomId);
    }

    public ResponseCheckOutAndLeave(puf: any) {
        logging.withTag('SDK').info('[SDK] ResponseCheckOutAndLeave');
        const msg = this.decodePB('ResponseCheckOutAndLeave', puf);
        if (msg) {
            cv.ToastError(msg.error);
        }
    }

    public NoticeCheckOutAndLeave(pbbuf) {
        const msg = this.decodePB('NoticeCheckOutAndLeave', pbbuf);
        logging.withTag('SDK').info('[SDK] NoticeCheckOutAndLeave', msg);
    }

    HandleResponsePlayerStake(puf: any) {
        logging.withTag('SDK').info('[SDK] HandleResponsePlayerStake');
        const msg = this.decodePB('NoticeUpdateMoney', puf);
        if (msg) {
            if (msg.room_id == cv.GameDataManager.tRoomData.u32RoomId) {
                cv.MessageCenter.send('update_player_stake', msg);
            }
        }
    }

    public static getInstance(): GameNetWork {
        if (!this.instance) {
            this.instance = new GameNetWork();
            this.instance.init();
        }
        return this.instance;
    }

    RequestPlayerBuyinsInfo(u32RoomId: number) {
        logging.withTag('SDK').info('[SDK] RequestPlayerBuyinsInfo');
        const sendGameMsg = { roomid: u32RoomId };
        const pbbuf = this.encodePB('RequestPlayerBuyinsInfo', sendGameMsg);
        this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_PlayerBuyinsInfo_Request, u32RoomId);
    }

    public ResponsePlayerBuyinsInfo(puf: any) {
        logging.withTag('SDK').info('[SDK] ResponsePlayerBuyinsInfo');
        const msg = this.decodePB('ResponsePlayerBuyinsInfo', puf);
        if (msg) {
        }
    }

    public NoticePlayerBuyinsInfo(puf: any) {
        logging.withTag('SDK').info('[SDK] NoticePlayerBuyinsInfo');
        const msg = this.decodePB('NoticePlayerBuyinsInfo', puf);
        if (msg) {
            cv.StringTools.deepCopy(msg.buyin_infos, cv.GameDataManager.tRoomData.buyinInfos);
            cv.MessageCenter.send('update_buyinInfo', msg.buyin_infos);
        }
    }

    // 暴击场开始消息
    public NoticeCritisicmStart(puf: any) {
        logging.withTag('SDK').info('[SDK] NoticeCritisicmStart');
        const msg = this.decodePB('NoticeCritisicmStart', puf);
        const u32RoomId = cv.GameDataManager.tRoomData.u32RoomId;
        if (msg.roomid == u32RoomId) {
            cv.MessageCenter.send('notice_critisicm_start', msg);
        }
    }

    // 暴击场金币不足
    public NoticeCritisicmNotEnoughMoney(puf: any) {
        logging.withTag('SDK').info('[SDK] NoticeCritisicmNotEnoughMoney');
        const msg = this.decodePB('NoticeNotEnougnMoney2Crit', puf);
        if (msg) {
            if (msg.roomid == cv.GameDataManager.tRoomData.u32RoomId) {
                cv.MessageCenter.send('notice_critisicm_not_enough', msg);
            }
        }
    }

    RequestAutoWithdraw(bopen: boolean) {
        logging.withTag('SDK').info('[SDK] RequestAutoWithdraw');
        const u32RoomId = cv.GameDataManager.tRoomData.u32RoomId;
        const sendGameMsg = { is_open: bopen };
        const pbbuf = this.encodePB('RequestAutoWithdraw', sendGameMsg);
        this.sendGameMsg(pbbuf, game_pb.MSGID.MsgId_AutoWithdraw_Request, u32RoomId);
    }

    public ResponseAutoWithdraw(puf: any) {
        logging.withTag('SDK').info('[SDK] ResponseAutoWithdraw');
        const msg: game_pb.ResponseAutoWithdraw = this.decodePB('ResponseAutoWithdraw', puf);
        if (msg) {
            if (msg.Error == 1) {
                cv.MessageCenter.send('withdraw_open', msg.is_open);
            }
        }
    }

    public buildBuyInsuranceMsg(
        outsCount: number,
        roomId: number,
        action_seq: number,
    ): IRequestBuyInsurance {
        const shouldBuyInsurance = this.shouldBuyInsurance(outsCount);

        if (!shouldBuyInsurance) {
            return {
                roomid: roomId,
                action_seq: action_seq,
                is_buy: false,
            };
        }

        const coverageAmount = this.selectCoverageAmount(outsCount);

        logging.withTag('INSURANCE_DECISION').info('Insurance decision made', {
            outsCount,
            shouldBuy: shouldBuyInsurance,
            amount: coverageAmount,
            roomId,
        });

        return {
            roomid: roomId,
            action_seq: action_seq,
            amount: coverageAmount,
            is_buy: true,
        };
    }

    public shouldBuyInsurance(outsCount: number): boolean {
        let buyProbability: number;

        if (outsCount <= 3) {
            buyProbability = 0.1; // Low probability - opponent has few ways to improve
        } else if (outsCount <= 5) {
            buyProbability = 0.25; // Medium-low probability - moderate opponent threat
        } else if (outsCount <= 7) {
            buyProbability = 0.35; // Medium probability - significant opponent threat
        } else {
            buyProbability = 0.55; // High probability - opponent has many ways to improve
        }

        return buyProbability > Math.random();
    }

    public selectCoverageAmount(outsCount: number): number {
        enum OutsCount {
            LOW = 3,
            MEDIUM = 7,
            HIGH = 12,
        }

        enum CoverageAmount {
            LOW = 12,
            MEDIUM_LOW = 20,
            MEDIUM_HIGH = 33,
            BREAK_EAVEN = 50,
            FULL_POT = 100,
        }

        let coverageWeights: { [amount: number]: number };

        if (outsCount <= OutsCount.LOW) {
            // Few opponent outs - lower risk, prefer smaller coverage amounts
            coverageWeights = {
                [CoverageAmount.LOW]: 50,
                [CoverageAmount.MEDIUM_LOW]: 35,
                [CoverageAmount.MEDIUM_HIGH]: 15,
            };
        } else if (outsCount <= OutsCount.MEDIUM) {
            // Medium opponent outs - moderate risk, balanced coverage
            coverageWeights = {
                [CoverageAmount.LOW]: 30,
                [CoverageAmount.MEDIUM_LOW]: 40,
                [CoverageAmount.MEDIUM_HIGH]: 20,
                [CoverageAmount.BREAK_EAVEN]: 10,
            };
        } else if (outsCount <= OutsCount.HIGH) {
            // More opponent outs - higher risk, prefer medium to high coverage
            coverageWeights = {
                [CoverageAmount.MEDIUM_LOW]: 25,
                [CoverageAmount.MEDIUM_HIGH]: 35,
                [CoverageAmount.BREAK_EAVEN]: 30,
                [CoverageAmount.FULL_POT]: 10,
            };
        } else {
            // Many opponent outs - high risk, prefer higher coverage amounts
            coverageWeights = {
                [CoverageAmount.MEDIUM_HIGH]: 20,
                [CoverageAmount.BREAK_EAVEN]: 40,
                [CoverageAmount.FULL_POT]: 40,
            };
        }

        return this.weightedRandomSelection(coverageWeights);
    }

    /**
     * Performs weighted random selection from a set of coverage amount options
     *
     *  more explanation behind weighted random algoritm could be find here
     *           https://dev.to/jacktt/understanding-the-weighted-random-algorithm-581p
     */
    public weightedRandomSelection(coverageAmountWeights: { [coverageAmount: number]: number }): number {
        const totalWeightSum = Object.values(coverageAmountWeights).reduce(
            (accumulatedWeight, currentWeight) => accumulatedWeight + currentWeight,
            0,
        );

        const randomThreshold = Math.random() * totalWeightSum;

        let runningWeightSum = 0;

        for (const [coverageAmountStr, weightValue] of Object.entries(coverageAmountWeights)) {
            runningWeightSum += weightValue;

            if (randomThreshold <= runningWeightSum) {
                const selectedCoverageAmount = parseInt(coverageAmountStr, 10);
                return selectedCoverageAmount;
            }
        }

        const fallbackCoverageAmount = parseInt(Object.keys(coverageAmountWeights)[0], 10);
        return fallbackCoverageAmount;
    }

    public NoticeGameInsurance(puf: any) {
        const msg = this.decodePB('NoticeGameInsurance', puf) as INoticeGameInsurance;

        logging.withTag('GAME_NETWORK').info('[SDK] NoticeGameInsurance', { payload: msg });

        if (msg) {
            const u32RoomId = cv.GameDataManager.tRoomData.u32RoomId;
            if (msg.roomid == u32RoomId && msg.buyer_uid == cv.dataHandler.getUserData().u32Uid) {
                const buyInsuranceMsg = this.buildBuyInsuranceMsg(msg.outs.length, u32RoomId, msg.action_seq);

                const pbbuf = this.encodePB('RequestBuyInsurance', buyInsuranceMsg);
                logging
                    .withTag('GAME_NETWORK')
                    .info('[SDK] RequestBuyInsurance', { payload: buyInsuranceMsg });

                setTimeout(
                    () => {
                        this.sendGameMsg(pbbuf, game_pb.MSGID.MsgID_BuyInsurance_Request, u32RoomId);
                    },
                    Math.random() * 2000 + 2000,
                );
            }
        }
    }
}
