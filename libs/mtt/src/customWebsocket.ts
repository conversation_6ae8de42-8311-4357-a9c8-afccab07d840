import { logging } from 'shared';
import { w3cwebsocket as W3CWebSocket } from 'websocket';

import { commonProto } from './mtt/pb/commonProto';
import { holdem } from './mtt/pb/holdem';
import { mttPro } from './mtt/pb/mtt';

import { WEBSOCKET_TYPE, WEBSOCKET_EVENT_ID } from './enums';

const { HttpsProxyAgent } = require('https-proxy-agent');

type MessageHandler = (msg?: any) => boolean | void | Promise<void>;
interface MessageHandlerMap {
    [key: number]: MessageHandler[];
}

interface PbMessages {
    [key: number]: any;
}

export class CustomWebsocket {
    private ws: InstanceType<typeof W3CWebSocket> | null = null;
    private pbIds: any;
    private messageSet: any;
    private tag: string;
    private pbMessages: PbMessages = {};
    private messageHandler: MessageHandlerMap = {};
    private websocketUrl: string;
    private connectionTimeout?: NodeJS.Timeout;
    private waitingPong: boolean = false;
    private pingInv?: NodeJS.Timeout;
    private pongInv?: NodeJS.Timeout;
    private pingMessageData: Uint8Array = new Uint8Array([0, 0, 0, 6, 0, 3]);
    private proxyUrl: string | null = null;
    private unknownMessages = new Set<number>();

    constructor(type: WEBSOCKET_TYPE, websocketUrl: string, proxyUrl: string) {
        this.websocketUrl = websocketUrl;
        this.proxyUrl = proxyUrl;

        if (type === WEBSOCKET_TYPE.WORLD) {
            this.pbIds = commonProto.SocketMessageId;
            this.messageSet = commonProto;
            this.tag = 'world';
        } else if (type === WEBSOCKET_TYPE.GAME) {
            this.pbIds = { ...holdem.MessageId, ...mttPro.MessageId };
            this.messageSet = { ...holdem, ...mttPro, MessageId: { ...holdem.MessageId, ...mttPro.MessageId } };
            this.tag = 'game';
        } else {
            throw new Error(`Unknown websocket type: ${type}`);
        }

        Object.keys(this.messageSet).forEach((k) => {
            if (this.pbIds[k] !== undefined) {
                this.pbMessages[this.pbIds[k]] = this.messageSet[k];
            }
        });
    }

    private intFromBytes(bytes: Uint8Array): number {
        let val = 0;
        for (let i = 0; i < bytes.length; ++i) {
            val += bytes[i];
            if (i < bytes.length - 1) {
                val = val << 8;
            }
        }
        return val;
    }

    private bytesFromInt(int: number, len: number): Uint8Array {
        const bytes = new Uint8Array(len);
        let i = len;
        do {
            bytes[--i] = int & 255;
            int = int >> 8;
        } while (i);
        return bytes;
    }

    private makeArray(msgBody: Uint8Array, msgId: number): Uint8Array {
        const tmp = new Uint8Array(msgBody.byteLength + 6);
        tmp.set(this.bytesFromInt(msgBody.byteLength + 6, 4), 0);
        tmp.set(this.bytesFromInt(msgId, 2), 4);
        tmp.set(msgBody, 6);
        return tmp;
    }

    public send(msgId: number, data: any): void {
        const pbObj = this.pbMessages[msgId];
        const msg = pbObj.create(data);
        const buff_content = pbObj.encode(msg).finish();
        logging.withTag('CUSTOM_WEBSOCKET').info(`Outgoing message: ${this.tag} => ${msgId}:${pbObj.pbName}`, data);
        try {
            this.ws?.send(this.makeArray(buff_content, msgId));
        } catch (error) {
            logging.error(`Error sending message: ${this.tag} => ${msgId}:${pbObj.pbName}`, error, data);
        }
    }

    addMessageHandler(msgId: number, handler: MessageHandler): void {
        if (this.messageHandler[msgId] === undefined) {
            this.messageHandler[msgId] = [];
        }
        this.messageHandler[msgId].push(handler);
    }

    removeMessageHandler(msgId: number, handler?: MessageHandler): void {
        if (this.messageHandler[msgId]) {
            if (handler) {
                const seek = this.messageHandler[msgId].indexOf(handler);
                if (seek !== -1) {
                    this.messageHandler[msgId].splice(seek, 1);
                }
            } else {
                this.messageHandler[msgId] = [];
            }
        }
    }

    private callHandler(msgId: number, msg?: any): void {
        if (this.messageHandler[msgId]) {
            this.messageHandler[msgId].forEach((handler) => {
                handler(msg);
            });
        }
    }

    private onopen = (): void => {
        clearTimeout(this.connectionTimeout);
        logging.withTag('CUSTOM_WEBSOCKET').info(`WebSocket onopen`);
        this.callHandler(WEBSOCKET_EVENT_ID.ON_CONNECTED);
    };

    private onclose = (event: CloseEvent): void => {
        clearTimeout(this.connectionTimeout);
        logging.withTag('CUSTOM_WEBSOCKET').info(`WebSocket onclose`, {
            code: event.code,
            reason: event.reason,
            wasClean: event.wasClean,
        });
        this.close();
        this.callHandler(WEBSOCKET_EVENT_ID.ON_CLOSE);
    };

    private onerror = (error: Error): void => {
        logging.withTag('CUSTOM_WEBSOCKET').error(`WebSocket onerror`, {...error, target: null});
    };

    private onmessage = (e: MessageEvent): void => {
        const buff = new Uint8Array(e.data);
        const len = this.intFromBytes(buff.slice(0, 4));
        const msgId = this.intFromBytes(buff.slice(4, 6));

        if (msgId === 3) { // ping
            return;
        }
        if (msgId === 4) {
            this.waitingPong = false;
            clearTimeout(this.pongInv);
            return;
        }

        if (len !== buff.length) {
            return;
        }

        const pbObj = this.pbMessages[msgId];
        if (pbObj) {
            const msg = pbObj.decode(buff.slice(6));
            logging
                .withTag('CUSTOM_WEBSOCKET')
                .info(`Incoming message: ${this.tag} <= ${msgId}:${pbObj.pbName}`, msg);
            this.callHandler(msgId, msg);
        } else {
            if (!this.unknownMessages.has(msgId)) {
                logging.warn(`Unknown incoming message ID (${this.tag}): ${msgId}`);
                this.unknownMessages.add(msgId);
            }
        }
    };

    connect(): void {
        logging.withTag('CUSTOM_WEBSOCKET').info(`MTT - connect`);
        this.forceClose();
        if (this.proxyUrl) {
            const agent = new HttpsProxyAgent(this.proxyUrl);
            // The only example of using the websocket  with proxy agent
            // https://github.com/theturtle32/WebSocket-Node/blob/master/docs/WebSocketClient.md
            this.ws = new W3CWebSocket(this.websocketUrl, null, null, null, {
                agent,
            });
            logging
                .withTag('CUSTOM_WEBSOCKET')
                .info(
                    `MTT - Initialized WebSocket connection (${this.websocketUrl}) with proxy: ${this.proxyUrl}`,
                );
        } else {
            this.ws = new W3CWebSocket(this.websocketUrl);
            logging
                .withTag('CUSTOM_WEBSOCKET')
                .info(`MTT - Initialized WebSocket connection (${this.websocketUrl}) without proxy`);
        }

        this.connectionTimeout = setTimeout(() => {
            logging.withTag('CUSTOM_WEBSOCKET').warn(`WebSocket connection timeout`);
            if (this.ws.readyState !== W3CWebSocket.OPEN) {
                this.ws.close();
            }
        }, 10000);

        this.ws.onerror = this.onerror;
        this.ws.onopen = this.onopen;
        this.ws.onclose = this.onclose;
        this.ws.onmessage = this.onmessage;
    }

    private close(): void {
        clearTimeout(this.pingInv);
        clearTimeout(this.pongInv);
        if (this.ws) {
            this.ws.close();
        }
    }

   forceClose(): void {
        if (this.ws) {
            this.ws.onerror = null;
            this.ws.onopen = null;
            this.ws.onclose = null;
            this.ws.onmessage = null;
        }
        this.close();
    }

    keepPing(): void {
        clearTimeout(this.pingInv);
        clearTimeout(this.pongInv);
        this.waitingPong = false;
        this.schedulePing();
    }

    private schedulePing(): void {
        this.pingInv = setTimeout(() => {
            if (this.ws?.readyState === W3CWebSocket.OPEN && !this.waitingPong) {
                this.ws?.send(this.pingMessageData);
                this.waitingPong = true;

                this.pongInv = setTimeout(() => {
                    if (this.waitingPong) {
                        logging
                            .withTag('CUSTOM_WEBSOCKET')
                            .info(`keepPing - pong lost`, { url: this.ws?.url });
                        this.connect();
                    }
                }, 3000);
            }
            this.schedulePing();
        }, 5000 + Math.random() * 1000);
    }
}
