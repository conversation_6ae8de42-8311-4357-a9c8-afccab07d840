const websocket = require("ws")

const client = function () {
    let ws = this.ws = {}, self = this;
    self.connect = function (url) {
        this.ws = ws = new websocket(url);
        self.url = url;
        ws.on("open", (data) => self.onopen(data))
        ws.on("message", (data) => self.onmessage(data));
        ws.on("error", function (err) {
            if (self.onerror) {
                self.onerror(err);
            } else {
                console.log("error: ", err.message);
            }
        });
        ws.on("close", function () {
            if (self.onclose) {
                self.onclose();
            } else {
                console.log("onclose")
            }
        });
        return ws;
    }
    self.reconnect = _ => self.connect(self.url);
}

module.exports = {
    client
};