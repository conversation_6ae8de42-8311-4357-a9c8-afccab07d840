const fs = require('fs');
const os = require('os');
const exec = require('child_process').exec;
const https = require('https');
const http = require('http');
const url = require('url');
const querystring = require('querystring');
const underscore = require('underscore');

const Utils = () => {
};
const U = Utils.U = underscore; // 工具库 https://underscorejs.org/    https://underscorejs.net/
// 空字符串判断
String.prototype.trim = function () {return this.replace(/^\s\s*/, '').replace(/\s\s*$/, '');}
// 判断是否包含任意字符串
String.prototype.contains=function(...s){for(let i=0;i<s.length;i++){if(this.indexOf(s[i])!=-1){return true;}}return false;}
// 日期格式化
Date.prototype.format=function(a){a=a||"yyyy-MM-dd hh:mm:ss";var c,b={"M+":this.getMonth()+1,"d+":this.getDate(),"h+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds(),"q+":Math.floor((this.getMonth()+3)/3),S:this.getMilliseconds()};/(y+)/.test(a)&&(a=a.replace(RegExp.$1,(this.getFullYear()+"").substr(4-RegExp.$1.length)));for(c in b){new RegExp("("+c+")").test(a)&&(a=a.replace(RegExp.$1,1==RegExp.$1.length?b[c]:("00"+b[c]).substr((""+b[c]).length)))}return a};
// 解析字符串到时间  yyyy-MM-dd hh:mm:ss
Date.prototype.parse = function (str) {
    let reg = /^(\d+)-(\d+)-(\d+) (\d+):(\d+):(\d+)/, s = str.match(reg), result = "";
    return s ? new Date(s[1], s[2] - 1, s[3], s[4], s[5], s[6]) : new Date();
}
// Object 过滤key
Object.prototype.filter = function(cb){
    let newObj = {}
    U.each(this, (v, k) => cb(v, k) && (newObj[k] = v));
    return newObj;
}

let envStr = null;
Utils.envStr = function () {
    if (envStr == null) {
        envStr = U.contains(process.argv, "test") ? "test" : "dev";
    }
    return envStr;
}

// 是否是开发环境
Utils.isDev = () => Utils.envStr() == "dev";

let getStackTrace = function () {
    let obj = {};
    Error.captureStackTrace(obj, getStackTrace);
    return obj.stack;
};
Utils.log = (...param) => {
    let fLine = "", cLog = (param[0] == "error" ? console.error : console.log), last = U.last(param), showLine = 2;
    // 如果有传递要显示的行数,则取出
    if (last && U.isString(last) && last.contains("stackTrace_")) {
        showLine = parseInt(last.split("_")[1]);
        param = param.slice(0, -1);
    }
    (getStackTrace() || "").split("\n")[showLine].replace(/\/([^/]*?):\d*?\)$/, (p, p2) => fLine = p2)
    cLog(new Date().format("hh:mm:ss.S ") + fLine + ":", ...param);
};

Utils.tryCatch = (cb, eCB) => {
    try {
        return cb()
    } catch (e) {
        return eCB && eCB(e);
    }
}

// http 请求
Utils.request = (param) => {
    let cHttp = param.url.indexOf("https") == 0 ? https : http;
    param.callback = param.callback || ((r,e) => {console.log(r,e)})
    let response = function (req, res) {
        let resp = '';
        req.on('data', (data) => resp += data);
        req.on('end', () => {
            try {resp = JSON.parse(resp);} catch (e) {}
            param.callback && param.callback(resp);
        });
    }
    let httpReq, nUrl = param.url,content = querystring.stringify(param.data);
    if (param.type == "get") {
        nUrl += (param.url.contains("?") ? "&" : "?") + content;
        httpReq = cHttp.get(nUrl, response);
    } else {
        let postOption = url.parse(nUrl);
        postOption.method = "POST"
        postOption.headers = {'Content-Type': 'application/x-www-form-urlencoded', 'Content-Length': content.length};
        httpReq = cHttp.request(postOption, response);
        httpReq.write(content);
        httpReq.end();
    }
    httpReq.on('error', e => param.callback(null,e));
}

// 当前执行node命令的目录地址
Utils.curPath = () => process.cwd();

// ************* 缓存工具 *************
// let cacheFile = os.homedir() + "/data/cache/cache.json", cacheMap = null;
// if (!Utils.isDev()) {
cacheFile = Utils.curPath() + "/../../cache.json";
cacheMap = null;
// }
console.log("缓存文件:", cacheFile);

let syncCache = U.throttle(() => Utils.writeFile(cacheFile, JSON.stringify(cacheMap)), 3000, {leading: false});// 每5秒同步一次
let cache = {
    getItem: key => {
        if (!cacheMap) {
            cacheMap = JSON.parse(Utils.tryCatch(() => Utils.readFile(cacheFile) || "{}", () => "{}"));
        }
        return cacheMap[key];
    },
    setItem: (key, val) => {
        cache.getItem(key);
        cacheMap[key] = val
        syncCache()
    }
}

// 设置缓存文件路径
Utils.setCacheFilePath = filePath => (cacheFile = filePath + "cache.json");

/**
 * 带有超时功能的缓存工具  (性能较差,不要用在大数据量和频率高的地方使用)
 * @param {string} key    缓存key [必须], 只传入key一个参数表示 ; 获取缓存,否则表示设置缓存
 * @param {object} value  任意类型 [可选]
 * @param {number} timeoutSec 超时时间[可选] 单位:秒  可为小数,0.5 表示500毫秒
 * 例: Utils.cache('name','123张三',60)
 */
Utils.cache = function (key, value, timeoutSec) {
    let isGet = !timeoutSec, isKey = "cache-mp", now = new Date().getTime();
    let cMap = cache.getItem(isKey) || {};
    let syncCache = () => cache.setItem(isKey, cMap);
    if (isGet) {
        let val = cMap[key];
        if (val && val.t >= now) return val.v;
    } else {
        cMap[key] = {t: now + timeoutSec * 1000, v: value};
        syncCache();
    }
    // 间隔一小时清理超时的缓存
    if (!Utils.isCD("clear-cache", 3600)) {
        for (const k in cMap) {
            if (cMap[k].t < now) delete cMap[k];
        }
        syncCache();
    }
};
/**
 * 异步获得缓存值
 * @param {string} key 缓存key [必须]
 * @param {number} timeoutSec 超时时间[必须] 单位:秒  可为小数,0.5 表示500毫秒
 * @param {function} defValCB 获得默认值的回调[必须] defValCB(defVal) ,defVal:缓存的默认值
 * @param {function} cb      获得缓存的回调[必须]
 * 例:
 Utils.asyncCache('name', 60,
 (cb) => setTimeout(() => cb('张三'),console.log("我发起请求拉") , 500), // 模拟发起请求
 (val) => console.log("获得缓存值:" + val)                             // 获得值回调
 );
 */
Utils.asyncCache = function (key, timeoutSec, defValCB, cb) {
    let val = Utils.cache(key);
    if (val) {
        cb(val);
    } else {
        defValCB((defVal) => {
            Utils.cache(key, defVal, timeoutSec);
            cb(defVal);
        });
    }
};

/**
 * 在规定时间内,只执行一次代码,避免短时间内重复执行(闭包TimeOut)
 * @return 例: var runTimeOut = Utils.TimeOutOne(500,func);使用 runTimeOut();
 */
Utils.TimeOutOne = function (time, func) {
    let index = null;
    return function () {
        index && clearTimeout(index);
        index = setTimeout(func, time);
    };
};

// 遍历数组,指定间隔(毫秒)
Utils.eachInterval = (arr, cb, intTime) => U.each(arr, (o, i) => setTimeout(() => cb(o, i), i * intTime));

/**
 * 是否在CD 冷却时间 的时间里, 控制调用间隔
 *
 * @param key  {String}  CD的key[必须]
 * @param cdSec {number} CD的时间[必须]  单位:秒  可为小数,0.5 表示500毫秒
 * @return {Boolean} 是否在CD
 */
Utils.isCD = function (key, cdSec) {
    let cdMap = cache.getItem("isCDMap") || {};
    let overtime = cdMap[key], curTime = new Date().getTime();
    let isCD = overtime && curTime <= overtime;
    // 清除过期的key
    for (let k in cdMap) {
        if (curTime > cdMap[k]) delete cdMap[k];
    }
    if (!cdMap[key]) cdMap[key] = curTime + cdSec * 1000;
    cache.setItem("isCDMap", cdMap);
    return !!isCD;
};

// 读取文件内容
Utils.readFile = function (filePath) {
    return fs.readFileSync(filePath, "utf-8");
};
// 写入文件内容
Utils.writeFile = function (filePath, txt) {
    return fs.writeFileSync(filePath, txt);
};

// 文件内容替换并保存
Utils.fileValReplace = function (filePath, searchValue, replacer) {
    var cfgStr = fs.readFileSync(filePath, "utf-8");
    cfgStr = cfgStr.replace(searchValue, replacer);
    fs.writeFileSync(filePath, cfgStr);
};

// 遍历获得文件夹下所有文件
Utils.loopDirSync = function (path) {
    function readDirSync(path, files) {
        var pa = fs.readdirSync(path);
        pa.forEach(function (ele, index) {
            var info = fs.statSync(path + "/" + ele)
            if (info.isDirectory()) {
                readDirSync(path + "/" + ele, files);
            } else {
                files.push((path + "/" + ele).replace("//", "/"))
            }
        })
    }
    let files = [];
    readDirSync(path, files);
    return files;
};


// 是否是linux
Utils.isLinux = function () {
    let sysType = os.type();
    return sysType !== "Windows_NT"
};

// 获得对象key列表
Utils.getObjKeys = function (obj) {
    let keys = []
    for (let key in obj) {
        keys.push(key)
    }
    return keys
};


/**
 * 字符串模版
 *例:
 *   var text = Utils.laytpl("{{d.name}}{{d.Utils.isLinux()}}").render({
 *       Utils:Utils,
 *       name:"张三"
 *   });
 */
Utils.laytpl = require('../libs/laytpl');


// 下划线转驼峰
Utils.camelCase = function (str) {
    return str.replace(/_([a-z])/g, function (all, letter) {
        return letter.toUpperCase();
    });
};

// 复制文本到剪切板
Utils.copyToClipboard = function (text) {
    // const { exec } = require('child_process')
    // if (process.platform === 'win32') {
    //     exec('clip').stdin.end(text)
    // } else if (process.platform === 'darwin') {
    //     exec('pbcopy').stdin.end(text)
    // } else {
    //     exec('xclip').stdin.end(text)
    // }
    const ncp = require("copy-paste")
    ncp.copy(text,  ()=> {});
};

// 获得剪切板中的文本内容
Utils.getClipboard = function () {
    const ncp = require("copy-paste")
    return ncp.paste();
}

// 执行系统命令
Utils.execCMD = function(cmd, callback) {
    require('child_process').exec(cmd, function (error, stdout, stderr) {
        if (error) {
            console.error(error);
        } else {
            callback(stdout)
        }
    });
}

// 睡眠  单位:毫秒
Utils.sleep = (delay) => {
    for (let t = Date.now(); Date.now() - t <= delay;);
}

// 写入字符串到文本文件,方便复制
Utils.consoleTxt = function (text) {
    fs.writeFileSync('console.txt', text);
};

// 判断字符是否为汉字，
Utils.isChinese = function (s) {
    return /[\u4e00-\u9fa5]/.test(s);
};

// 编码   中文 转 unicode
Utils.encodeUnicode = function (str) {
    if (!str) {
        return;
    }
    return str.replace(/[\u0080-\uffff]/g, function (ch) {
        return '\\u' + ('0000' + ch.charCodeAt(0).toString(16)).slice(-4);
    });
};

// 解码   unicode 转 中文
Utils.decodeUnicode = function (str) {
    return str.replace(/\\u([\dA-Fa-f]{4})/g, function (match, grp) {
        return String.fromCharCode(parseInt(grp, 16));
    });
};

// 获取 guid
Utils.guid = function () {
    let S4 = () => (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
};


// 指定随机种子的伪随机数  initSeed:种子, genNum:生成的个数. 生成范围:min~max(包含)
Utils.seededRandom = (initSeed, genNum, min, max) => {
    let _seed = initSeed, a = 9301, b = 49297, m = 233280
    const sRandom = _ => {
        _seed = (_seed * a + b) % m
        return _seed / m
    }
    return U.map(U.range(genNum), n => parseInt(sRandom() * (max - min + 1) + min, 10));
}

// 随机生成中文自 (用作随机生成昵称)
Utils.randomChinese = function (num) {
    let min = 0x4e00, max = 0x9fa5
    return Utils.generateArr(num || 2,()=>Utils.decodeUnicode("\\u" + Math.floor(Math.random() * (min - max) + max).toString(16))).join("");
}

// 随机指定长度的字符串
Utils.randomStr = function (num, str) {
    str = str || "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
    let sLen = str.length;
    return Utils.generateArr(num || 2, () => str.charAt(U.random(0, sLen - 1))).join("");
}

// 随机昵称 (常用中文字加字母)
Utils.randomNickname = function (num) {
    let str = "abcdefghijklmnopqrstuvwxyz清冷的等待遗失过去尼姑庵少女掩面说心倳生活真无趣我伟刚勇毅俊峰强军平保东文辉力明永健世广志义兴良海山仁波宁贵福龙元全国胜学祥才发武新利飞彬富顺信子杰涛昌成康星光天达安岩中茂进林有坚和彪博诚先敬震振壮会凤洁梅琳素云莲环雪荣爱妹霞香月莺媛艳瑞凡佳嘉琼勤珍贞莉桂娣叶璧璐娅琦晶妍茜秋珊莎锦黛青倩婷姣婉娴瑾颖露瑶怡婵雁蓓纨仪荷丹蓉眉君琴蕊薇菁梦岚苑婕馨瑗琰韵融园艺咏卿聪澜纯毓悦昭冰爽琬茗羽希欣飘育滢馥筠柔竹喵酱木棉絮躲日曦顽皮捣蛋精灵爷那么黑萌音草莓芭比人鱼烟带疏又三蔷水闲独身迷漾随绪没见他泪茉溪深巷孤盗洛筱眼名字渺秘密纸殇魅最地奶气缠哭鬼薰衣景琉暮知了铃朝阳鸾脸梓娘唯答案故蜜糖顆甜入穷尉蓝而极老冬顾北亡张沁淡余惜辞染兽声夜儛埗味千囚愿帝孩高候鸟菠萝黄事外似茴菿原乌团主系盖傻游戏间聊净陪界结辣条然仲需要绿岁尖樱風廻喑残苔珺婳邪金泫離低细喃街倾忧伤蒙陵忻看坏灯旅怀开好菇枯释放毛蟲绡柒槿碧所诗砉繁陞洇吹弦断暧梵十后鬓茶挽阴多种幸还偶尔想起太动嗲限能何茵尽萱馋边篱涵代祭米像寞灰貓浥語抹谷咸宛境出头古大漠意笙怜早难缤纷粉酸果谱写旋律拭陽袖冒险货尛儍汝澍绵梧桐舀瓢醺瞌睡窝川吖棒咕道抱哖怪贝书荼靡干昵称首共词笔隐记关集市收藏饮困雕刻鹤桉临已久桑皋俞卯凛苼盼喻帘楼冥滴九四鳁旻宫蔓霖旧柚橙楚沈匿迟堪片携昏漏拂逗逼愉袭扬著曲琵琶徒留桥亦庭前吻浓搖灣酷炸口幼版祖宗闻羊饭龟奖油饼狙击拾者耗佐理杀吧唧尸走肥肉害羞百堇渐性剩绯向曾衍款笺钟鸽芷含阿栩且赴逸迢觞阡翎痕杨替五改这位靓您爹打倒乖锅包求篇卟智服务区藕连族创始转巳牛仔刘分狂拽及腰容伴厕作墙苹哥禁问坟蹦迪脾麻婆豆热陶凯赐源遂甲乙喜玫瑰银袅瓣追圈养泛交猖征碍至丑薯病根捧乡幔藤妆沧痂誓剑佩苘芜井绘扎话酌惊罐熊洼耍嘛猪崽狗亲药伙衬处炒栗傀儡抓法馆湘笨用买马渣蜗鞭疼票卡丘拯救胡卜帅非叔抠脚杠血尿裤助式烈占加勒煮够畏惧吗也卖拖网速男洒当氧跟左右每秒鸡跳输"
    return Utils.randomStr(num, str);
}

// 用 cb()方法,生成num长度的数组
Utils.generateArr = function (num,cb){
    let arr = [];
    for (let i = 0; i < num; i++) {
        arr.push(cb(i))
    }
    return arr;
}

// 字符串下划线转驼峰  font_size => fontSize
Utils.toHump = str => str.replace(/_(\w)/g, (_, c) => c ? c.toUpperCase() : '');





module.exports = Utils;
