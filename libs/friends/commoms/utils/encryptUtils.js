const CryptoJS = require('../libs/crypto-js.min');
const JSEncrypt = require('../libs/jsencrypt.min');
const pako = require('../libs/pako.min');
const base64 = require('../libs/base64.min');

const EncryptUtils = () => {
};

// u8array Plug
CryptoJS.enc.u8array={stringify:function(a){var e,f,b=a.words,c=a.sigBytes,d=new Uint8Array(c);for(e=0;c>e;e++)f=255&b[e>>>2]>>>24-8*(e%4),d[e]=f;return d},parse:function(a){var d,b=a.length,c=[];for(d=0;b>d;d++)c[d>>>2]|=(255&a[d])<<24-8*(d%4);return CryptoJS.lib.WordArray.create(c,b)}};
function convertWordArrayToUint8Array(a){let arrayOfWords=a.hasOwnProperty("words")?a.words:[];let length=a.hasOwnProperty("sigBytes")?a.sigBytes:arrayOfWords.length*4;let uInt8Array=new Uint8Array(length),index=0,word;for(let i=0;i<length;i++){word=arrayOfWords[i];uInt8Array[index++]=word>>24;uInt8Array[index++]=(word>>16)&255;uInt8Array[index++]=(word>>8)&255;uInt8Array[index++]=word&255}return uInt8Array};
function utf8ArrayToStr(a){for(var e,f,g,b="",d=a.length,c=0;d>c;)switch(e=a[c++],e>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:b+=String.fromCharCode(e);break;case 12:case 13:f=a[c++],b+=String.fromCharCode((31&e)<<6|63&f);break;case 14:f=a[c++],g=a[c++],b+=String.fromCharCode((15&e)<<12|(63&f)<<6|(63&g)<<0)}return b}

// MD5 加密
EncryptUtils.MD5 = str => CryptoJS.MD5(str).toString().toUpperCase();

// AES 加密字符串
EncryptUtils.encryptAES = function (str,sKey) {
    let realKey = CryptoJS.SHA1(CryptoJS.SHA1(sKey)).toString().substring(0, 32); // 真正的key
    let key =  CryptoJS.enc.Hex.parse(realKey),srcs = CryptoJS.enc.Utf8.parse(str);
    return CryptoJS.AES.encrypt(srcs, key, {mode:CryptoJS.mode.ECB,padding: CryptoJS.pad.Pkcs7}).toString();
}

// AES 解密字符串
EncryptUtils.decryptAES = function (str,sKey) {
    let realKey = CryptoJS.SHA1(CryptoJS.SHA1(sKey)).toString().substring(0, 32); // 真正的key
    let key = CryptoJS.enc.Hex.parse(realKey),content = CryptoJS.enc.Base64.parse(str);
    return CryptoJS.AES.decrypt({ciphertext: content}, key, {mode:CryptoJS.mode.ECB,padding: CryptoJS.pad.Pkcs7}).toString(CryptoJS.enc.Utf8);
}

// AES 解密字节数组
EncryptUtils.decryptAESBytes = function (contentBytes,sKey) {
    let u8array = new Uint8Array(contentBytes);
    let data = CryptoJS.enc.u8array.parse(u8array).toString(CryptoJS.enc.Base64);
    let realKey = CryptoJS.SHA1(CryptoJS.SHA1(sKey)).toString().substring(0, 32); // 真正的key
    let key = CryptoJS.enc.Hex.parse(realKey);
    let decrypted = CryptoJS.AES.decrypt(data, key, {mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7});
    return convertWordArrayToUint8Array(decrypted);
}

// RSA 加密
EncryptUtils.encryptRSA = function (str,pubKey) {
    let encryptor = new JSEncrypt()
    encryptor.setPublicKey(pubKey)
    return encryptor.encrypt(str)
}

// RSA 解密
EncryptUtils.decryptRSA = function (str,priKey) {
    let encryptor = new JSEncrypt()
    encryptor.setPrivateKey(priKey)
    return encryptor.decrypt(str)
}

// base64 编码
EncryptUtils.encodeBASE64 = function (str) {
    return base64.encode(str)
}

// base64 解码
EncryptUtils.decodeBASE64 = function (str) {
    return base64.decode(str)
}

// gzip压缩字符串
EncryptUtils.gzip = function (str) {
    let binaryString = pako.gzip(str, {to: 'string'});
    return base64.encode(binaryString);
}

// gzip解压字符串
EncryptUtils.ungzip = function (str) {
    let charData = base64.decode(str).split('').map(x => x.charCodeAt(0));
    return utf8ArrayToStr(pako.inflate(new Uint8Array(charData)));
}

module.exports = EncryptUtils;
