syntax = "proto3";
import "google/protobuf/wrappers.proto";
package com.hm.wepoker.battle.controller.proto;
option java_multiple_files = true;
option java_generate_equals_and_hash = false;
message MsgDeliverResp {
    google.protobuf.Int32Value errorCode = 1; // 返回码， 0标示成功，其他参考错误码定义类
    google.protobuf.StringValue errMsg = 2; // 错误信息，默认为空字符串
    google.protobuf.Int64Value sysTime = 3; // 系统的时间戳**用于矫正手机本地时间不准的问题
    google.protobuf.StringValue msgType = 4; // 消息类型
    bytes msgBody = 5; // 消息内容
    google.protobuf.StringValue originOpt = 6; // 触发操作来源
    google.protobuf.StringValue callbackId = 7; // 消息类型
    google.protobuf.StringValue optHandle = 8; // 操作id(消息验证)
    google.protobuf.StringValue protoType = 9;
    google.protobuf.StringValue newToken = 10;
}
message BattleDealCardPOList {
    repeated BattleDealCardPO list = 1;
}
message BattleDealCardPO {
    /**
     * 手牌list
     */
    repeated google.protobuf.Int32Value handCards = 1;
    /**
     * 用户ID
     */
    google.protobuf.Int64Value userId = 2;
    /**
     * 卡牌类型
     */
    google.protobuf.StringValue cardType = 3;
    /**
    * 游戏状态
    */
    google.protobuf.Int32Value gameStatus = 4;

}
message BattleRoomUserMsg {
    google.protobuf.StringValue nickname = 1;  // 昵称
    google.protobuf.StringValue avatar = 2; // 头像
    google.protobuf.Int32Value sex = 3;// 性别
    google.protobuf.Int32Value seatNum = 4; // 座位号
    repeated google.protobuf.Int32Value handCards = 5; // 手牌
    repeated google.protobuf.Int32Value publicCards = 6;// 公开手牌
    BattleActionPO actionPO = 7; // 用户操作
    google.protobuf.Int32Value gameStatus = 8;
    google.protobuf.StringValue brandType = 9;// 牌型
    repeated google.protobuf.Int32Value maxCardCombination = 10;
    google.protobuf.Int64Value userId = 11;
    google.protobuf.Int32Value currentScore = 12;
    google.protobuf.Int32Value buyScore = 13;
    google.protobuf.Int32Value inRoomStatus = 14;
    google.protobuf.Int32Value isOnline = 15;
    google.protobuf.Int64Value countDown = 16;
    google.protobuf.StringValue cardType = 17;
    google.protobuf.Int32Value preOptType = 18;
    google.protobuf.Int32Value playBoutNum = 19;
    repeated google.protobuf.Int32Value publicHandCards = 20;// 公开手牌
    google.protobuf.Int32Value optHandle = 21;// 操作标识数
    BattleRoomDetailResp roomDetail = 22;
    google.protobuf.Int32Value currRankNum = 23;
    google.protobuf.StringValue remark = 24;// 备注昵称
    repeated google.protobuf.Int32Value highHandCards = 25;// 高亮牌型
    google.protobuf.StringValue remarkDetail = 26;// 备注昵称
    google.protobuf.Int32Value squidWin = 27; // 鱿鱼游戏胜利场次
    google.protobuf.StringValue realHead = 28;// 真人头像(用于真人gif头像验证)
}
message BattleSimpleUserPO {
    google.protobuf.StringValue nickname = 1; // 昵称
    google.protobuf.StringValue avatar = 2;// 头像
    google.protobuf.Int32Value sex = 3; // 性别
    google.protobuf.Int64Value userId = 4;
    google.protobuf.Int32Value inRoomStatus = 5;
    google.protobuf.Int32Value buyScore = 6;
    google.protobuf.Int32Value currentScore = 7;
    google.protobuf.Int64Value createTime = 8;
    google.protobuf.Int32Value playBoutNum = 9;
    google.protobuf.Int32Value isAdmin = 10;
    google.protobuf.Int32Value currRankNum = 11;
    google.protobuf.Int32Value sngRank = 12;
    google.protobuf.Int32Value changeScore = 13;
    google.protobuf.Int32Value squidWin = 14;
}
message BattleRoomDetailResp {
    repeated BattleSimpleUserPO lookList = 1; // 旁观用户列表
    repeated BattleSimpleUserPO buyScoreList = 2; // 买入分数列表
    google.protobuf.Int32Value gameFund = 3; // 游戏总组局基金
    google.protobuf.Int32Value curGameFund = 4; // 当前游戏基金
    google.protobuf.Int32Value currentBoutNum = 5; // 当前手数
    google.protobuf.Int32Value totalBuyScore = 6; // 总买入分数
    google.protobuf.Int32Value totalDealScore = 7; // 总流水分数
    google.protobuf.Int32Value avgPotScore = 8; // 平均底池
    google.protobuf.Int32Value duration = 9; // 总时长（分）
    google.protobuf.Int64Value residueSec = 10; // 剩余秒数
    google.protobuf.DoubleValue gameFundRate = 11;
    google.protobuf.Int32Value isAdmin = 12;
    google.protobuf.Int32Value errorCode = 13; // 返回码， 0标示成功，其他参考错误码定义类
    google.protobuf.StringValue errMsg = 14; // 错误信息，默认为空字符串
    google.protobuf.Int64Value sysTime = 15; // 系统的时间戳**用于矫正手机本地时间不准的问题
    google.protobuf.Int32Value curSB = 16;
    google.protobuf.Int32Value nextSB = 17;
    google.protobuf.Int32Value usedSec = 18;
    google.protobuf.Int32Value useWallet = 19; // 是否使用钱包
    google.protobuf.Int32Value forceLive = 20;
    google.protobuf.Int64Value insuranceTotal = 21;//保险总计
}
message GradeCfgPO {
    google.protobuf.Int32Value bigBlind = 1;
    google.protobuf.Int32Value smallBlind = 2;
    google.protobuf.Int32Value ante = 3;
    google.protobuf.Int32Value buyScore = 4;
    google.protobuf.Int32Value collectNum = 5; //收藏消耗钻石
    google.protobuf.Int32Value lookPublicCardNum = 6; //看公牌消耗钻石
    repeated DealProlongCfg cfgList = 7;
    google.protobuf.Int64Value upBlindTime = 8;
    google.protobuf.DoubleValue fundCapBb = 9;
}

message DealProlongCfg {
    google.protobuf.Int32Value dealType = 1;
    google.protobuf.Int32Value amount = 2;
    google.protobuf.Int32Value timeSec = 3;
}
message BattleRoomMsg {
    google.protobuf.Int32Value optSeatNum = 1; // 操作用户Id
    repeated BattleRoomUserMsg sitUserList = 2; // 坐下用户列表
    google.protobuf.Int64Value roomId = 3;
    google.protobuf.Int32Value roomNumber = 4; // 6位数房间号
    google.protobuf.StringValue title = 5; // 房间标题
    google.protobuf.Int64Value clubId = 6; // 俱乐部ID
    google.protobuf.Int64Value masterUserId = 7; // 房主用户Id
    google.protobuf.Int32Value roomStatus = 8; // 房间状态
    google.protobuf.Int64Value createTime = 9; // 创建时间
    google.protobuf.Int32Value gamePersonNum = 10; // 游戏人数
    google.protobuf.Int32Value currentBoutNum = 11; // 当前手数
    GradeCfgPO gradeCfg = 12; // 游戏等级
    google.protobuf.Int32Value isNeedAgreeSit = 13; // 是否需要同意坐下
    google.protobuf.Int32Value minScoreMultiple = 14; // 代入最小分数倍数
    google.protobuf.Int32Value maxScoreMultiple = 15; // 代入最大分数倍数
    google.protobuf.Int32Value duration = 16; // 时长
    google.protobuf.Int64Value startTime = 17; // 开始时间
    google.protobuf.Int32Value isAnte = 18; // 是否需要前注
    google.protobuf.Int32Value gameFund = 19; // 组局基金
    google.protobuf.Int32Value isSendDouble = 20; // 是否支持发2次
    google.protobuf.Int32Value roomType = 21; // 房间类型
    google.protobuf.Int32Value isLimitIp = 22;
    google.protobuf.Int64Value bankerUserId = 23;
    repeated google.protobuf.Int32Value publicCards = 24; // 公共牌
    google.protobuf.StringValue round = 25; // 下注圈
    google.protobuf.BoolValue isPause = 26; // 是否暂停
    google.protobuf.Int64Value countDown = 27; // 当前倒计时（秒）
    google.protobuf.Int64Value totalCountDown = 28; // 当前倒计时（秒）
    google.protobuf.Int32Value currentUserSeatNum = 29; // 当前用户座位号
    google.protobuf.Int32Value bBSeatNum = 30; // 大盲位
    google.protobuf.Int32Value isDoubleAnte = 31; // 是否开启庄家两倍ante
    google.protobuf.Int32Value playType = 32; // 玩法类型
    google.protobuf.Int32Value sBSeatNum = 33; // 小盲位
    google.protobuf.Int32Value totalPot = 34; // 全部底池
    repeated google.protobuf.Int32Value potList = 35; // 分池
    google.protobuf.StringValue clubName = 36;
    google.protobuf.StringValue masterNickname = 37;
    google.protobuf.StringValue masterAvatar = 38;
    BattleActionPO curAction = 39;
    google.protobuf.Int32Value residueSec = 40;
    BattleProlongPO prolongCfg = 41;
    google.protobuf.Int32Value bankerSeatNum = 42;
    google.protobuf.Int32Value curRoundMaxBetScore = 43;
    repeated google.protobuf.Int32Value oldPublicCards = 44;
    repeated google.protobuf.Int32Value highPublicCards = 45;
    BattleDoubleThanResultMsg oldThanResult = 46;
    BattleDoubleThanResultMsg thanResult = 47;
    BattleProlongPO seeComCardCfg = 48; // 延时PO
    google.protobuf.Int32Value shortCompareType = 49;
    google.protobuf.DoubleValue gameFundRate = 50; // 组局基金比率
    google.protobuf.Int32Value isLimitScore = 51;
    google.protobuf.Int64Value originOptUserId = 52;
    BattleRoomDetailResp roomDetail = 53;
    google.protobuf.Int32Value currRankNum = 54;
    google.protobuf.Int32Value grade = 55;
    google.protobuf.Int64Value usedSec = 56;
    google.protobuf.Int32Value isRandSeat = 57; // 是否开启随机入座  1:是,0:否
    BattleLiveInfoPO liveInfo = 58;
    BattleForceSeeCardCfgPO forceSeeCardCfg = 59;
    google.protobuf.Int32Value isThirdBlind = 60;
    google.protobuf.Int32Value tBSeatNum = 61;
    google.protobuf.Int32Value minBuyInScore = 62;
    google.protobuf.Int32Value maxBuyInScore = 63;
    google.protobuf.Int32Value isAdmin = 64;
    google.protobuf.Int32Value bigPotNum = 65;
    google.protobuf.Int32Value mulTableUserNum = 66;
    google.protobuf.Int64Value lastCritTime=67;
    google.protobuf.Int32Value critBBTimes=68;//-1 等小于0表示不开启
    google.protobuf.Int32Value critMomentType=69;//0表示 翻牌前， 1表示翻牌后
    google.protobuf.Int32Value critMinute=70;//暴击时间周期 分钟为单位 ，但是是秒数
    google.protobuf.Int32Value isInsurance = 71;//是否是保险牌局
}
message BattleLiveInfoPO {
    google.protobuf.Int32Value forceLive = 1;
    google.protobuf.StringValue liveToken = 2;
    google.protobuf.StringValue liveChannel = 3;
    google.protobuf.Int32Value allowLive = 4;
    google.protobuf.Int32Value allowVoice = 5;
    google.protobuf.Int32Value allowJoinLive = 6;
}
message BattleProlongPO {
    google.protobuf.Int32Value btnType = 1; // 按钮样式
    google.protobuf.Int32Value needDiamond = 2; // 花費鑽石
    google.protobuf.Int32Value canNextProlong = 3; // 能否進行下一次延時
    google.protobuf.Int32Value freeTimes = 4; // 免费次数
}
// 强制查看所有玩家手牌配置
message BattleForceSeeCardCfgPO {
    google.protobuf.Int32Value isCanSee = 1;
    google.protobuf.Int32Value needDiamond = 2;
    google.protobuf.Int32Value seeNum = 3;
    google.protobuf.Int32Value freeTimes = 4; // 免费次数
}
message BattleActionPO {
    google.protobuf.StringValue actionType = 1; // 操作类型
    google.protobuf.Int32Value seatScore = 2; // 用户座位上分数
    google.protobuf.Int32Value actionScore = 3; // 本次操作分数
    google.protobuf.Int32Value card = 4; // 操作卡牌
    google.protobuf.Int64Value userId = 5; // action 用户Id
    google.protobuf.Int64Value seatNum = 6; // 座位号
    google.protobuf.Int32Value currentScore = 7; // 用户当前分数
    google.protobuf.Int32Value isShowAllin = 8; // 是否展示ALLIn 标识
    google.protobuf.Int32Value sex = 9; // 用户线性别
    google.protobuf.Int64Value thinkTime = 10; // 思考时长
}
message BattleDoubleThanResultMsg {
    google.protobuf.Int32Value isDogfall = 1; // 是否平手
    google.protobuf.Int64Value winUserId = 2; // 赢家用户ID
    google.protobuf.StringValue winNickname = 3; // 赢家昵称
    repeated google.protobuf.Int32Value oldPublicCards = 4; // 旧的公共牌（需要上移）
    google.protobuf.StringValue drawNickname = 5;
}
message BattleBankChangeMsg {
    google.protobuf.Int64Value bBUserId = 1;
    google.protobuf.Int64Value sBUserId = 2;
    google.protobuf.Int64Value bankerUserId = 3;
    google.protobuf.Int32Value bBSeatNum = 4;
    google.protobuf.Int32Value sBSeatNum = 5;
    google.protobuf.Int32Value bankerSeatNum = 6;
}
message BattleActionMsg {
    repeated BattleActionPO actionList = 1;
    google.protobuf.Int32Value totalPot = 2; // 全部底池
    BattleRoomUserMsg selfInfo = 3;
}
message BattleUserOptMsg {
    google.protobuf.Int64Value userId = 1;
    google.protobuf.Int32Value minRaiseScore = 2;
    google.protobuf.Int32Value maxRaiseScore = 3;
    google.protobuf.Int32Value callScore = 4;
    repeated google.protobuf.StringValue canActionList = 5;
    google.protobuf.Int64Value countDown = 6;
    google.protobuf.Int64Value totalCountDown = 7;
    google.protobuf.Int32Value lastBet = 8;
    google.protobuf.Int32Value seatScore = 9;
    BattleProlongPO prolongCfg = 10;
    repeated google.protobuf.Int32Value handCards = 11;
    google.protobuf.StringValue cardType = 12;
    google.protobuf.Int32Value isCanRaise = 13; // 是否可以加注
    google.protobuf.BoolValue openRetainSeat = 14;
}
message BattleRoundChangeMsg {
    google.protobuf.Int32Value totalPot = 1; // 全部底池
    repeated google.protobuf.Int32Value potList = 2; // 分池
    repeated google.protobuf.Int32Value dealPublicCards = 3; // 发送公牌
    google.protobuf.StringValue cardType = 4; // 卡牌类型
    repeated BattleUserCardInfoMsg userCardsList = 5; // 用户卡牌列表
    google.protobuf.Int32Value isDoubleRound = 6; // 是否发2次流程
    BattleUserOptMsg userAciton = 7; // 用户操作通知
    google.protobuf.StringValue round = 8;
}
message BattleAddScoreStatusResp {
    google.protobuf.Int32Value status = 1;
}
message BattleThanResp {
    google.protobuf.Int32Value isAllUserAllin = 1;
    google.protobuf.Int32Value isDealDouble = 2;
    repeated BattleThanPO thanList = 3;
    BattleRoomDetailResp roomDetail = 4;
    repeated google.protobuf.Int32Value highPublicCards = 5;
}
message BattleProlongMsg {
    google.protobuf.Int64Value userId = 1; // 用戶ID
    google.protobuf.Int64Value countDown = 2; // 倒計時
    google.protobuf.Int64Value balanceDiamond = 3; // 鑽石餘額
    google.protobuf.Int32Value btnType = 4; // 按钮样式
    google.protobuf.Int32Value needDiamond = 5; // 花費鑽石
    google.protobuf.Int32Value canNextProlong = 6; // 能否進行下一次延時
    google.protobuf.Int32Value freeTimes = 7; // 免费次数
}
message ChatMsgPO {
    google.protobuf.Int64Value userId = 1; // 用户ID
    google.protobuf.Int32Value type = 2; //": "text",//消息类型 text emoji
    google.protobuf.StringValue content = 3; //":"你好！",//文本内容 or emoji编码 见附2
    google.protobuf.StringValue avatar = 4; //头像
    google.protobuf.StringValue nikename = 5; //昵称
    google.protobuf.Int32Value second = 6;
    google.protobuf.Int64Value receiverId = 7;
    google.protobuf.Int32Value num = 8;
    google.protobuf.Int32Value emojiId = 9;
    google.protobuf.Int32Value aniType = 10;
    google.protobuf.Int64Value createTime = 11;
    google.protobuf.StringValue nickname = 12; //昵称
    google.protobuf.Int32Value availableFreeEmojiNum = 13; //可用次数
    google.protobuf.StringValue id = 14; // 消息ID
    google.protobuf.Int32Value regFreeEmoji = 15; //注册赠送可用次数
}
message BattleSendDoubleOptMsg {
    google.protobuf.Int32Value validPotScore = 1;
    repeated google.protobuf.Int32Value publicCards = 2;
    repeated BattleSendDoubleUserMsg battleUserList = 3;
    google.protobuf.Int64Value countDown = 4;
}
message BattleCSDResultMsg {
    google.protobuf.Int64Value userId = 1; //":1000,
    google.protobuf.StringValue nickname = 2; //":"nickname",
    google.protobuf.StringValue avatar = 3; //":"http://dsfadsf.jpg",
    google.protobuf.Int32Value chooseStatus = 4; //":0
}
message AllinOpenCardMsg {
    repeated BattleUserCardInfoMsg userCardInfoList = 1;
    google.protobuf.Int32Value totalPot = 2;
    repeated google.protobuf.Int32Value potList = 3;
}
message BattleOpenCardMsg {
    google.protobuf.Int64Value userId = 1;
    google.protobuf.Int32Value actionCard = 2; // 操作的卡牌
    repeated google.protobuf.Int32Value publicCards = 3; // 已亮出的牌型
    google.protobuf.Int32Value optType = 4; // 操作类型
    google.protobuf.StringValue cardType = 5; // 卡牌类型
}
message MessageDoorPO {
    google.protobuf.Int64Value doorId = 1;
    google.protobuf.StringValue title = 2;
    google.protobuf.StringValue icon = 3;
    google.protobuf.StringValue content = 4;
    google.protobuf.StringValue contentParam = 5;
    google.protobuf.Int64Value updateTime = 6;
    google.protobuf.Int32Value unReadCount = 7; // 未读消息数
    google.protobuf.Int32Value doorType = 8; // 消息种类 0-系统 1-俱乐部消息 2-牌局消息
    google.protobuf.Int32Value unDealCount = 9; // 未处理消息数
    google.protobuf.BoolValue isShowTip = 10; // 是否展示提醒
}
message RoomPlayRecordOneHand {
    google.protobuf.Int32Value handNum = 1;
    google.protobuf.Int32Value frequency = 2;
    repeated BattlePlayLog handList1 = 3;
    repeated BattlePlayLog handList2 = 4;
    google.protobuf.Int32Value insuranceGain = 5;
}
message LookComCardsMsg {
    google.protobuf.Int64Value userId = 1;
    google.protobuf.Int32Value handNum = 2;
    google.protobuf.Int32Value needDiamond = 3;
    repeated google.protobuf.Int32Value seeComCards = 4;
    google.protobuf.StringValue nickname = 5;
    google.protobuf.Int32Value isShow = 6;
    google.protobuf.Int32Value freeSeeComs = 7;
}
message WaitHandResp {
    google.protobuf.Int32Value waitHandNum = 1;
}
message BattlePlayLog {
    google.protobuf.Int64Value recordId = 1;
    google.protobuf.Int64Value userId = 2;
    google.protobuf.Int64Value roomId = 3;
    google.protobuf.StringValue publicCard = 4;
    google.protobuf.StringValue handCard = 5;
    google.protobuf.Int32Value changeScore = 6;
    google.protobuf.Int64Value createTime = 7;
    google.protobuf.Int64Value lookbackId = 8;
    google.protobuf.Int64Value winUserId = 9;
    google.protobuf.StringValue winAvatar = 10;
    google.protobuf.Int32Value winerChangeScore = 11;
    google.protobuf.Int32Value ante = 12;
    google.protobuf.Int32Value grade = 13;
    google.protobuf.Int32Value pooling = 14;
    google.protobuf.Int32Value showdown = 15;
    google.protobuf.Int32Value folpRaise = 16;
    google.protobuf.Int32Value reraise = 17;
    google.protobuf.Int32Value continuationBet = 18;
    google.protobuf.Int32Value win = 19;
    google.protobuf.Int32Value handNum = 20;
    google.protobuf.Int32Value seatNum = 21;
    google.protobuf.StringValue cardType = 22;
    google.protobuf.StringValue betList = 23;
    google.protobuf.Int32Value keyCard = 24;
    google.protobuf.StringValue seatPos = 25;
    google.protobuf.Int32Value dealIndex = 26;
    google.protobuf.StringValue avatar = 27;
    google.protobuf.Int32Value showcardType = 28;
    google.protobuf.StringValue publicCardAll = 29;
    google.protobuf.Int32Value playType = 30;
    google.protobuf.StringValue omahaShow = 31;
    google.protobuf.StringValue seeComCards = 32;
    google.protobuf.Int32Value seeAuCards = 33;
    google.protobuf.Int32Value isBigPot = 34;
    google.protobuf.Int32Value squidWin = 35;
    google.protobuf.StringValue nickname = 36;
    google.protobuf.Int32Value insuranceWin= 37;
    google.protobuf.Int32Value insuranceInvest= 38;
    google.protobuf.Int32Value  insuranceRiverInvest= 39;
}
message BattleUserCardInfoMsg {
    google.protobuf.Int64Value userId = 1;
    repeated google.protobuf.Int32Value publicCards = 2;
    google.protobuf.StringValue cardType = 3;
    google.protobuf.Int32Value isShowdown = 4;
}
message BattleSendDoubleUserMsg {
    google.protobuf.Int64Value userId = 1;
    google.protobuf.Int32Value betScore = 2;
    repeated google.protobuf.Int32Value handCards = 3;
    google.protobuf.DoubleValue winProb = 4;
    google.protobuf.StringValue nickname = 5;
    google.protobuf.StringValue avatar = 6;
}
message BattleThanPO {
    google.protobuf.Int64Value userId = 1; // 用户ID
    google.protobuf.Int32Value changeScore = 2; // 更改分数
    google.protobuf.Int32Value changeAfterScore = 3; // 更改后分数
    google.protobuf.Int32Value isHighlight = 4; // 是否 需要高亮 0,1
    repeated google.protobuf.Int32Value highHandCards = 5; // 高亮牌型
    repeated google.protobuf.Int32Value publicCards = 6; // 手牌
    google.protobuf.StringValue cardType = 7; // 卡牌类型
    google.protobuf.Int32Value addScore = 8; // 增加的分数
    google.protobuf.Int32Value seatNum = 9; // 座位号
    google.protobuf.Int32Value isShowdown = 10; // 是否摊牌
    google.protobuf.Int32Value fund = 11; // 抽水
    google.protobuf.Int32Value fundFirst = 12; // 第一次抽水
    google.protobuf.Int32Value fundSecond = 13; // 第二次抽水
    google.protobuf.Int32Value addScoreFirst = 14; // 第一次发牌赢分数
    google.protobuf.Int32Value addScoreSecond = 15; // 第二次发牌赢分数
    google.protobuf.Int32Value changeScoreFirst = 16; // 第一次发牌增加分数
    google.protobuf.Int32Value changeScoreSecond = 17; // 第二次发牌增加分数
    google.protobuf.Int32Value squidScore = 18; // 鱿鱼游戏结算积分数
    google.protobuf.Int32Value insuranceResult = 19; // 保险结算分
}
message RaiseBlindMsg {
    google.protobuf.Int32Value nextSB = 1;
    google.protobuf.Int32Value curSB = 2;
    google.protobuf.Int64Value residueSec = 3;
}
//强制看所有玩家手牌通知
message ForceSeeCardResp {
    int64 userId = 1;
    int32 handNum = 2; // 游戏手数
    repeated BattleHandCardPO handCards = 3; // 用户手牌列表
    BattleForceSeeCardCfgPO forceSeeCardCfg = 4;
}
// 用户手牌
message BattleHandCardPO {
    google.protobuf.Int64Value userId = 1;
    repeated google.protobuf.Int32Value handCards = 2; // 手牌
}
// 跑马灯消息通知
message MarqueeMsgResp {
    int32 repeatCount = 1;
    google.protobuf.StringValue content = 2;
    int32 specifyType = 3;
}
// 基础响应体
message BaseResp {
    google.protobuf.Int32Value errorCode = 1;
    google.protobuf.StringValue errMsg = 2;
    google.protobuf.Int64Value sysTime = 3;
}
message PersonalData{
    google.protobuf.Int32Value poolingHandNum = 1;
    google.protobuf.Int32Value winNum = 2;
    google.protobuf.Int32Value showdownNum = 3;
    google.protobuf.Int32Value folpRaiseNum = 4;
    google.protobuf.Int32Value reraiseNum = 5;
    google.protobuf.Int32Value continuationBet = 6;
    google.protobuf.Int64Value userId = 7;
    google.protobuf.Int32Value totalHand = 8;
    google.protobuf.StringValue signature = 9;
}
message BattleUserProfileResp{
    repeated PersonalData listData = 1;
}
message StarInfo{
    google.protobuf.StringValue pic = 1;
    repeated google.protobuf.StringValue topic = 2;
}
message StarGameMsg{
    int64 roomId = 1;
    repeated StarInfo star = 2;
    repeated google.protobuf.StringValue text = 3;
}
// MTT开赛通知
message MttStartMsg{
    int64 roomId = 1; // MTT房间ID (用于客户端跳转)
    google.protobuf.StringValue content = 2; // 多语言消息内容(包含秒${sec}替换符) {"zh":"","cn":"","en":"","ru":""}
    google.protobuf.StringValue name = 3; // 多语言 赛事名称 {"zh":"","cn":"","en":"","ru":""}
    int32 seconds = 4; // 开赛剩余时间 (单位:秒)
}
message UpdateUserDiamondBalance{
    google.protobuf.DoubleValue diamondBalance = 1;
    google.protobuf.Int64Value userId = 2;
    google.protobuf.DoubleValue giftDiamondBalance = 3;
    google.protobuf.DoubleValue gift2Balance = 4;
}
// 赠送钻石
message DiamondRespMsgPO{
    google.protobuf.Int64Value sourceUserId = 1;
    google.protobuf.Int64Value targetUserId = 2;
    google.protobuf.Int32Value diamondId = 3;
    google.protobuf.Int32Value num = 4;
    google.protobuf.Int32Value remainCount = 5;
    google.protobuf.StringValue ext = 6;
}
// 活动红包通知
message RedPackMsg{
    int64 sourceId = 1;     // 来源用户ID(赠送者用户ID)
    int64 targetId = 2;     // 红包目标用户ID(接收者用户ID)
    int32 type = 3;         // 红包类型 1:钻石,2:通用体验金,3:mtt门票
    float num = 4;          // 数量/金额
    int32 remainCount = 5;  // 剩余赠送次数
    google.protobuf.StringValue ext = 6; // 扩展参数 (方便以后扩展)
}
// 注单内的下注项赛事详情
message SportTicketsOptions {
    google.protobuf.Int64Value matchId = 1;
    google.protobuf.Int32Value sportId = 2;
    google.protobuf.StringValue matchName = 3;
    google.protobuf.Int32Value matchState = 4;
    google.protobuf.Int64Value kickOffTime = 5;
    google.protobuf.Int64Value tournamentId = 6;
    google.protobuf.StringValue tournamentName = 7;
    google.protobuf.Int32Value tournamentLevel = 8;
    google.protobuf.Int32Value homeScore = 9;
    google.protobuf.Int32Value awayScore = 10;
    google.protobuf.Int64Value marketId = 11;
    google.protobuf.Int32Value marketType = 12;
    google.protobuf.Int32Value marketGroup = 13;
    google.protobuf.Int32Value marketStage = 14;
    google.protobuf.StringValue betBar = 15;
    google.protobuf.StringValue sabaMarketId = 16;
    google.protobuf.StringValue sabaBetOption = 17;
    google.protobuf.DoubleValue odds = 18;
    google.protobuf.Int64Value optionId = 19;
    google.protobuf.StringValue betOption = 20;
    google.protobuf.Int64Value settleTime = 21;
    google.protobuf.StringValue settleScore = 22;
    google.protobuf.Int32Value settleResult = 23;
}
// 注单内的下注项信息
message SportTicketsBets {
    google.protobuf.Int32Value betM = 1;
    google.protobuf.Int32Value betN = 2;
    google.protobuf.Int32Value betCount = 3;
    google.protobuf.DoubleValue betAmount = 4;
}
// 体育注单
message SportTicketsRecords {
    google.protobuf.StringValue ticketId = 1;
    google.protobuf.DoubleValue betAmount = 2;
    google.protobuf.Int32Value ticketStatus = 3;
    google.protobuf.StringValue sabaTicketId = 4;
    google.protobuf.StringValue compUserId = 5;
    google.protobuf.Int32Value betType = 6;
    google.protobuf.Int32Value currency = 7;
    google.protobuf.Int64Value betTime = 8;
    google.protobuf.StringValue domain = 9;
    google.protobuf.Int32Value clientType = 10;
    google.protobuf.Int32Value appId = 11;
    google.protobuf.StringValue ip = 12;
    google.protobuf.DoubleValue settleAmount = 13;
    google.protobuf.Int32Value settleResult = 14;
    google.protobuf.Int64Value settleTime = 15;
    google.protobuf.BoolValue reSettle = 16;
    google.protobuf.DoubleValue beforeAmount = 17;
    google.protobuf.DoubleValue afterAmount = 18;
    repeated SportTicketsOptions options = 19;
    repeated SportTicketsBets bets = 20;
}
message UpdateUserBalance{
    google.protobuf.DoubleValue afterAmount = 1;
    google.protobuf.DoubleValue afterUSDTAmount = 2;
    google.protobuf.Int32Value errorCode = 3;
}

// 牌桌体育注单通知消息
message QuickSportTicketsRespMsgPO {
    google.protobuf.Int64Value targetUserId = 1;    // 目标用户ID（接收者用户ID）
    SportTicketsRecords ticketsInfo = 2;       // 注单信息
}
message DeskSportNoticeMsg{
    google.protobuf.StringValue moreOneHoursStr = 1;
    google.protobuf.StringValue lessOneHoursStr = 2;
    google.protobuf.StringValue startStr = 3;
    google.protobuf.Int64Value sportStart = 4;
    google.protobuf.Int32Value cdTime = 5;
    google.protobuf.Int32Value showTimes = 6;
}
// 基础数据对象(单一数据,不明确某类型消息,方便扩展其他消息)
message BaseDataPO {
    google.protobuf.StringValue data = 1;
}
// 聚合支付信息
message PayMatchInfoPO {
    google.protobuf.Int32Value status = 1;
    google.protobuf.Int64Value endTime = 2;
    google.protobuf.DoubleValue amount = 3;
    google.protobuf.Int32Value type = 4;
    google.protobuf.Int32Value coins = 5;
    google.protobuf.Int32Value isRead = 6;
    google.protobuf.BoolValue read = 7;
}
// 撤回积分 消息通知
message RetraceScoreMsgPO {
    int32 scene = 1; // 通知场景 1:进入房间数据更新, 2:结算时,撤回积分通知, 3:下一手生效
    int32 type = 2;  // 撤回类型 1: 手动撤回, 2:自动撤回
    int32 score = 3; // 撤回的积分数
    int32 totalScore = 4;  // 累计撤回的积分数
    google.protobuf.Int32Value keepScore = 5;    // 补撤记分,上限积分数 0:表示无限(通知场景1,才有此值)
    google.protobuf.Int32Value minKeepScore = 6; // 补撤记分,下限积分数(通知场景1,才有此值)
}

// 鱿鱼游戏 消息通知
message BattleSquidGameMsg {
    int32 scene = 1;
    repeated google.protobuf.Int64Value users = 2;  // 用户集
    repeated SquidGameSettPO sett = 3; // 结算信息
    google.protobuf.StringValue ext = 4; // 扩展参数 (方便以后扩展)
}

// 鱿鱼游戏-结算信息
message SquidGameSettPO {
    int64 userId = 1;   // 用户ID
    int32 addScore = 2; // 本次增加的积分 (正数表示奖励,负数表示惩罚)
    int32 curScore = 3; // 用户变更后的当前积分
}

// 求签提示信息
message BeghintMsg{
    google.protobuf.Int32Value status = 1;//状态 为0 表示正常 其他情况则表示出错-1 表示未开放，-3 余额不足
    google.protobuf.Int32Value hintType = 2;   // 运势类型 1 上上，2上，3上平，4中上，5 中 6 中下
    google.protobuf.Int32Value luckpoint = 3; // 运势得分 客户端需 /10 用于显示
    google.protobuf.Int32Value richpoint = 4; // 财运得分 客户端需 /10 用于显示
    google.protobuf.StringValue hintMsg = 5; //提示信息 签语
    google.protobuf.Int64Value nextBegtime = 6;//签语剩余时间
    google.protobuf.Int32Value price = 7;  //求签价格
}

/**
投保信息通知,客户端判断是否需要展示购买项，已经已经购买的选中效果
 */
message InsuranceMsgNotify{
    google.protobuf.Int32Value status = 1;////状态为0表示正常，其他则表示存在问题,1，不是这个时机 ，无需购买，2，不确定是哪个分池 3，没有outs 匹配的前提下不用购买，4，最大底池有牌力相当的两个人领先，不提供保险 ，5，最大底池中人数超过3人。不提供保险，6，未达到购买条件， 赔率低于0.1，7，保险命中outs， 8，保险未命中outs， 9，保险底池低于50不需要购买保险
    google.protobuf.Int32Value alreadyInsurance = 2;//已经购买的保险类型 -1，不买，0，未购买，1，满池  2，1/2池  3，1/3池 ， 4，1/8池 5，1/5池 6，保本,7，只购买背保险额
    InsuranceInfo info = 3 ;//保险概况
}

/**
 投保基础信息
 */
message InsuranceInfo{
    google.protobuf.Int64Value insuranceId = 1;//投保玩家id
    google.protobuf.Int32Value buyType= 2;//购买类型，0 不强制，1 强制购买，其他待扩展
    google.protobuf.Int32Value maxInsurancePot =3;//底池
    google.protobuf.Int32Value canInsurancePot=4;//可投保底池
    google.protobuf.Int32Value betInsurance =5;//下注额
    google.protobuf.DoubleValue insuranceOdds= 6;//赔率
    repeated google.protobuf.Int64Value userIds =7;//按照顺序显示的玩家id 2-3个
    repeated google.protobuf.Int32Value otherOutsNum= 8;//其他玩家的outs 数量， 第一个位置是购买玩家的0， 第二个以后就是其他玩家的outs数量
    google.protobuf.Int32Value insuranceMoney= 9;//投保额(当已操作投保后会有该信息）
    google.protobuf.Int32Value insuranceTarget= 10;//投保标的 （保险触发时收益）
    google.protobuf.DoubleValue insuranceTax = 11;//手续费？（保险触发时,有小数点）
    google.protobuf.Int32Value turnInsuranceBuy=12;//购买转牌保险额，（保险触发时）
    repeated InsuranceCardType cards = 13;//所有牌类型
    google.protobuf.Int64Value nextOpTime = 14;//房间下次的更新操作时间。专门用于扩展给客户端使用
    google.protobuf.Int64Value totalOpTime = 15;//房间更新操作总时间。专门用于扩展给客户端使用
    BattleProlongPO prolongCfg = 16;//延时信息
    google.protobuf.Int32Value insuranceScore= 17;//投保玩家当前积分值
}

/**
卡牌类型枚举
1.投保玩家手牌，2-5张
2.其他玩家手牌，每个人 2-5张，按照玩家名称顺序显示
3，弃牌玩家手牌，有out效果
4，弃牌玩家手牌，无out效果
5.公牌
6.剩余牌 有out效果
7。剩余牌 无out效果
8.参与玩家手牌最大牌型（高亮牌）
9.剩余牌 equal outs效果牌
10.投保玩家授牌最大牌型 （高亮牌）
*/
message InsuranceCardType{
    google.protobuf.Int32Value card = 1;//牌
    google.protobuf.Int32Value type = 2;//
}


