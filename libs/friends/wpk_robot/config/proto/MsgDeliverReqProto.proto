syntax = "proto3";
import "google/protobuf/wrappers.proto";
package com.hm.wepoker.battle.controller.proto;
option java_multiple_files = true;
option java_generate_equals_and_hash = false;
//请求 基本信息
message MsgDeliverReq {
    int64 targetId = 1; // 目标房间ID
    string msgType = 2; // 消息类型
    bytes msgBody = 3;  // 消息内容
    string sign = 4;    // 签名
    string callbackId = 5; // 回调ID
    MsgUserInfo stubInfo = 6; // 用户基本资料
    int32 optHandle = 7; // 操作id(消息验证)
}
//用户基本信息
message MsgUserInfo {
    int64 userId = 1;  // 用户id
    string nickname = 2; // 昵称
    string avatar = 3; // 头像
    int32 sex = 4;     // 性别0 未设置	1男 2 女
    string sessionToken = 5; // 会话token
    string version = 6; // app版本号
}
//加注
message BattleRaisePO {
    int32 raiseScore = 1;
    int32 reSend = 2;
}
// 申请与检查坐下
message BattleSitMsg {
    int32 seatNum = 1;
    double lon = 2; // 经度
    double lat = 3; // 纬度
}
//亮牌
message BattleOpenCardReq {
    int32 card = 1;
    int32 optType = 2; //0隱藏 1顯示
}
//自动操作
message BattleAutoOptPO {
    int32 preOptType = 1; //0：取消 1 :让或弃, 2;让， 3：跟注
}
//买分
message BattleScorePO {
    int32 score = 1;
    string passwd = 2;
}
//选择发2次
message BattleDoubleChooseReq {
    int32 chooseStatus = 1;
}
//聊天消息
message MsgTypeMsgPO {
    int32 type = 1;
    string content = 2;
    int32 second = 3;
    int64 createTime = 4;
    string id = 5; // 消息ID
}
//emoji
message EmojiMsgPO {
    int32 type = 1;
    string content = 2;
    int64 receiverId = 3;
    int32 num = 4;
    int32 emojiId = 5;
    int32 aniType = 6;
}
//强制站起
message ForceStandReq {
    int64 standUserId = 1;
}
// 看公牌/强制看牌
message LookComCardsReq {
    int32 handNum = 1;
    int32 reSend = 2;
}
// 解除/屏蔽 发言
message ShieldUserMsgReq {
    int32 type = 1;
    int64 targetUserId = 2;
}
// 操作目标(不明确某类型消息,方便扩展其他消息)
message OptTargetPB {
    int64 targetId = 1; // 操作目标(目标用户ID)
    string ext = 2;     // 扩展参数(方便扩展其他消息)
}
// 钻石赠与
message DiamondSendMsgPO {
	int64 receiverId = 1;
	int32 diamondId = 2;
  string passwd = 3; // 支付密码
}

// 猜手牌 投注 与 续投设置
message GuessHandBetPO {
    int32 type = 1; // 投注类型 1: 投注, 2:续投设置
    int32 bet = 2; // 投注项  1:表示投注项1, 2: 表示投注项2
    int32 oddsId = 3; // 投注赔率 类型ID
    int32 continuous = 4; // 续投配置 1:开启续投, -1:关闭续投
}

// 撤回积分 请求参数
message RetraceScorePO {
    int32 type = 1;  // 撤回类型 1: 手动撤回, 2:自动撤回
    int32 score = 2; // 撤回的积分数  0:不撤回
}

// 加入房间请求参数
message JoinRoomReq {
    int32 type = 1;  // 加入类型 1: 预加载数据(只通知C_updateRoomNotify),  0/其他值: 正常进入
}
//Chinese poker msgs start from here
//Place card
message PlaceCardPO{
    string location = 1;
    int32 index = 2;
    int32 card = 3;
}

//used for placing multiple cards at once
message MultiPlaceCardPO{
    repeated PlaceCardPO locations = 1;
    string specialHandType = 2;
    int32 moveCard = 3;
}

//Replace card
message ReplaceCardPO{
    repeated PlaceCardPO locations = 1;
}


//Swap
message SwapCardPO{
    repeated PlaceCardPO locations = 1;
}

//to set the bet
message BetPO{
    int32 value = 1;
}

//sort
message SortPO{
    string orderBy = 1;
}

message SpecialHandComparisonDecisionPO{
    string useSpecialHand = 1;
}
// 真人gif头像验证
message RealHeadPO {
    google.protobuf.StringValue head = 1;
    google.protobuf.Int64Value userId = 2;
}

/**
购买牌局保险操作
 */
message InsuranceBuyPO{
    int32 type = 1;//-1，不买，0，未购买，1，满池  2，1/2池  3，1/3池 ， 4，1/8池 5，1/5池 6，保本，7，仅购买背保险
    int32 buyRound = 2;//购买回合  ，1 为翻牌 2为转牌
}
