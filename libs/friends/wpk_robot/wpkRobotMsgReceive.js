const Utils = require("../commoms/utils/nodeCommonUtils");
const roomService = require("./wpkRobotRoom");
const roomUserService = require("./wpkRobotRoomUser");
const RobotConst = require("./common/wpkRobotConst");
const {all} = require("underscore");
const U = Utils.U;
const PokerOdds = require('poker-odds');
const {deal} = require("poker-odds/lib/deck");

// 房间信息
let roomMap = {}, room = id => {
    let roomId = typeof id === 'string' ? id : [id.roomId, id.userId];
    return (roomMap[roomId] = roomMap[roomId] || {publicCards: {}});
};

const ACTION_MAP = {
    "FOLD": "fold",
    "CALL": "call",
    "CHECK": "check",
    "RAISE": "raise",
    "ALL_IN": "allin",
    "BET": "bet"
}

// reverse key value in ACTION_MAP to value to key
const STRATEGY_TO_ACTION = Object.entries(ACTION_MAP).reduce((acc, [key, value]) => {
    acc[value] = key;
    return acc;
}, {});

const ACTION_TO_MESSAGE = {
    "CHECK": "WP_check",
    "CALL": "WP_call",
    "RAISE": "WP_raise",
    "FOLD": "WP_fold",
    "ALL_IN": "WP_allin"
}

const showCards = function (card) {
    let convert = (c) => ({"1": "A", "10": "T", "11": "J", "12": "Q", "13": "K"}[c % 100] || c % 100) + ["s", "h", "c", "d"][parseInt(c / 100) - 1];
    return Array.isArray(card) ? card.map(c => convert(c)).join("") : convert(card);
}


function weighted_random(actions) {
    let total = 0;
    for (let i = 0; i < actions.length; i++) {
        total += actions[i].hand_strategy;
    }
    let random_num = Math.random() * total;
    let weight_sum = 0;
    for (let i = 0; i < actions.length; i++) {
        weight_sum += actions[i].hand_strategy;
        if (random_num <= weight_sum) {
            return actions[i];
        }
    }
    return actions[actions.length - 1];
}

function capAmounts(state, bbMaxCap = 500, stackMaxCap = 500) {
    return {
        ...state,
        actions: {
            ...state.actions,
            entries: state.actions.entries.map(a => {
                if (a.amount) {
                    return {...a, amount: Math.min(a.amount, bbMaxCap * state.big_blind)};
                }
                return {...a};
            })
        },
        players: state.players.map(p => ({
            ...p,
            stack: Math.max(Math.min(p.stack, stackMaxCap * state.big_blind), 1)
        }))
    };
}

function flattenState(state) {
    return {
        ...state,
        actions: state.actions.entries
    };
}

function winProbabilities(hand, pubCards, numOfPlayers) {
    const hands = [hand.map(showCards), ...Array(numOfPlayers - 1).fill(['..', '..'])]
    let board = pubCards.map(showCards)
    let result = PokerOdds.calculateEquity(hands, board);
    // console.info("Win probabilities", hands, board, result[0])
    return (result[0].wins + result[0].ties) / result[0].count;
}

function logi(ws, msg, props) {
    const state = room(ws).state;
    console.info({
        "timestamp": new Date().toISOString(),
        "roomId": ws.roomId,
        "userId": ws.userId,
        "msg": msg,
        ...props,
        "gameuuid": state.gameuuid,
    });
}

function logw(ws, msg, props) {
    const state = room(ws).state;
    console.warn({
        "timestamp": new Date().toISOString(),
        "roomId": ws.roomId,
        "userId": ws.userId,
        "msg": msg,
        ...props,
        "state": JSON.stringify(state),
        "gameuuid": state.gameuuid,
    });
}

function loge(ws, msg, err, props) {
    const state = room(ws).state;
    console.error({
        "timestamp": new Date().toISOString(),
        "roomId": ws.roomId,
        "userId": ws.userId,
        "msg": msg,
        "error": err,
        ...props,
        "state": JSON.stringify(state),
        "gameuuid": state.gameuuid,
    });
}

class MsgReceive {
    constructor() {
        this.msgReceiveMap = {
            "C_updateRoomNotify": [this.updateRoomNotify, this.cleanState, this.updateGameState, this.updateSeats],
            "C_updateRoomUserNotify": [this.updateRoomUserNotify],
            "WP_bankerChangeNotify": [this.updateSeats],
            "WP_userOptNotify": [this.userOptNotify,this.actionOpt],
            "WP_roundChangeNotify": [this.updateActionCards,this.roundChangeNotify],
            "WP_actionNotify": [this.actionNotify,this.updateActions],
            "WP_dealNotify": [this.dealNotify, this.updateUserCards],
            "C_closeRoomNotify": [this.onCloseRoomNotify],
        };
    }

    cleanState(ws, msgType, body) {
        room(ws).state = {
            "gameuuid": ws.roomId + "_" + body["currentBoutNum"] + "_" + ws.userId + "_" + new Date().getTime(),
            "game_type": "nlhe",
            "game_mode_code": "normal",
            "actions": {"entries": []},
            "players": [],
        };
    }

    updateGameState(ws, msgType, body) {
        // logi(ws, "updateGameState", {"payload": JSON.stringify(body)});
        let state = room(ws).state;
        let players = []
        for (let p of body["sitUserList"]) {
            if (p['gameStatus'] < 100 || p['gameStatus'] == 103) {
                players.push({
                    "uid": p["userId"],
                    "seat_no": p["seatNum"] - 1,
                    "stack": p["currentScore"],
                });
            } else {
                logw(ws, "player might not in a game", p);
            }
        }
        state["players"] = players.sort((a, b) => a.seat_no - b.seat_no);
        state["roomid"] = body["roomId"];
        state["big_blind"] = body["gradeCfg"]["bigBlind"];
        state["ante"] = body["gradeCfg"]["ante"];
    }

    updateSeats(ws, msgType, body) {
        // logi(ws, "updateSeats", {"payload": JSON.stringify(body)});
        let state = room(ws).state;
        state["dealer_seat"] = body["bankerSeatNum"] - 1;
        state["sb_seat"] = body["sBSeatNum"] - 1;
        state["bb_seat"] = body["bBSeatNum"] - 1;
        if ("tBSeatNum" in body) {
            state["straddle_seat"] = body["tBSeatNum"] - 1;
        }
    }

    updateActions(ws, msgType, body) {
        let state = room(ws).state;
         for (let a of body["actionList"]) {
            if (a["actionType"] in ACTION_MAP) {
                let action = {
                    "action": ACTION_MAP[a["actionType"]]
                };
                if (["RAISE", "ALL_IN", "BET"].includes(a["actionType"])) {
                    action["amount"] = a["actionScore"];
                }
                state.actions.entries.push(action);
            } else if (a["actionType"] == "SMALL_BLIND") {
                // logi(ws, "updateActions", {"sb_seat": a["seatNum"]});
                state["sb_seat"] = parseInt(a["seatNum"]) - 1;
            } else if (a["actionType"] == "BIG_BLIND") {
                // logi(ws, "updateActions", {"bb_seat": a["seatNum"]});
                state["bb_seat"] = parseInt(a["seatNum"]) - 1;
            } else if (a["actionType"] == "THIRD_BLIND") {
                // logi(ws, "updateActions", {"straddle_seat": a["seatNum"]});
                state["straddle_seat"] = parseInt(a["seatNum"]) - 1;
            }
         }
    }

    updateActionCards(ws, msgType, body) {
        let state = room(ws).state;
        if (["FLOP", "TURN", "RIVER"].includes(body["round"])) {
            let action = {
                "action": showCards(body["dealPublicCards"])
            };
            state.actions.entries.push(action);
        }
    }

    updateUserCards(ws, msgType, body) {
        let state = room(ws).state;
        let playerState = state.players.find(p => p.uid == ws.userId);
        let playerData = body.list.find(c => c.userId == ws.userId);
        if (playerState && playerData) {
            playerState["hole_cards"] = showCards(playerData["handCards"]);
        }
    }

    msgReceive(ws, msgType, body, resp) {
        // if (msgType || body) {
        //     console.debug(new Date().format("hh:mm:ss.S ") + " " + msgType, body)
        // }
        if (resp.errorCode != 0) {
            ws.log("error", `${resp.msgType} errorCode:${resp.errorCode}, errMsg:${resp.errMsg}`);
        }
        ws.receiveLog && msgType && ws.log(`WS <- ${msgType}:`, body, "\n");
        try {
            let func = this.msgReceiveMap[msgType],self = this;
            func && U.each(func, f => f.call(self, ws, msgType, body, resp));
        } catch (e) {
            ws.log("error", "msgReceive", msgType, resp.errorCode, resp.errMsg, e);
            console.error(e);
        }
    }

    updateRoomNotify(ws, msgType, body) {
        let user = roomUserService.getRoomUser(body.sitUserList, ws.userId);
        // 离开状态,马上回到座位
        if (user && user.gameStatus == 103) {
            ws.send("WP_backSeat")
        }
        if (!Utils.isCD("updateRoomOpt" + ws.userId, 1)) {
            let isAddScore = false
            // 积分过少, 买积分
            if (user && user.currentScore < body.minBuyInScore) {
                ws.send("WP_addScore", {score: body.minBuyInScore})
                ws.log("买积分:" + body.minBuyInScore);
                isAddScore = true;
            }
            // setTimeout(() => !user && ws.M.sit(body), isAddScore ? 1000 : 1);
        }
    }

    updateRoomUserNotify(ws, msgType, body) {
        // 房间等待状态 房主尝试开始
        if (roomService.isCanStartGame(body)) {
            ws.send("C_startGame")
        }
    }

    // 用户操作通知
    userOptNotify(ws, msgType, body) {
    }

    // 操作通知
    actionNotify(ws, msgType, body) {
        room(ws).totalPot = body.totalPot;
    }

    // 发牌通知
    dealNotify(ws, msgType, body) {
        // console.log("dealNotify");
        let rInfo = room(ws);
        // 记录房间发牌时间点
        rInfo.dealTime = new Date().getTime();
        rInfo.publicCards = {};
        // filter out players that are not in the game
        let userIds = body.list.map(u => u.userId);
        rInfo.state.players = rInfo.state.players.filter(p => userIds.includes(p.uid));
    }

    // 流程切换操作通知
    roundChangeNotify(ws, msgType, body) {
        if (Utils.isCD("roundChange" + ws.userId, 0.8)) {
            return;
        }
        let prob = U.random(1, 100), rInfo = room(ws);
        // 收集发出的公牌
        if (body.dealPublicCards && U.contains(["FLOP", "TURN", "RIVER"], body.round)) {
            rInfo.publicCards[body.round] = body.dealPublicCards;
        }
        // 6% 发送聊天/表情消息
        if (prob <= 6 && !Utils.isCD("C_imMsg" + ws.userId, 10)) {
            let data = prob <= 3 ? ["C_imMsg", {"content": U.sample(RobotConst.robotImMsg)}] : ["C_emoji", {"content": "emoji" + U.random(1, 24)}];
            roomService.delayOpt(() => ws.send(data[0], data[1]), 800, 5000);
        }
        this.actionOpt(ws, msgType, body)
    }

    actionOpt(ws, msgType, body) {
        let userAction = body.userAciton || body, canActionList = userAction.canActionList || [], rInfo = room(ws), dealTime = rInfo.dealTime || 0;
        if (U.isEmpty(canActionList) || ws.userId != userAction.userId) {
            return;
        }

        let state = rInfo.state;
        let playerState = state.players.find(p => p.uid == ws.userId);
        if (playerState && userAction.handCards) {
            playerState["hole_cards"] = showCards(userAction.handCards);
        }

        // ws.log("state", flattenState(room(ws).state), "\n")

        this.strategyOpt(ws, msgType, body)
            .then(() => {})
            .catch(e => {
                const stackLines = e.stack.split('\n');
                const errorLine = stackLines[1] ? stackLines[1].trim() : 'unknown line';
                loge(ws, "failed to execute strategy action", e.message, {"errorLine": errorLine});
                this.fallbackOpt(ws, msgType, body)
            });
        // this.fallbackOpt(ws, msgType, body)
    }

    async strategyOpt(ws, msgType, body) {
        let dealTime = room(ws).dealTime || 0;
        if (dealTime === 0) {
            throw new Error("joined after the game began, actions data may be incomplete");
        }

        let services = RobotConst.cfg.strategyServices || ["RL"];
        let strategyHost = RobotConst.cfg.strategyHost || "gto-glue-svc.priv.prod.kashxa-infra.com";
        const strategyUrl = `http://${strategyHost}/lookup?${services.map(s => "services="+s).join("&")}`
        let strategyData = {
            "request_id": new Date().getTime().toString(),
            "hand": room(ws).state,
            "hints": RobotConst.cfg.hints || [],
            "mode": "bot",
        };
        if (services[0] === "SUBGAME_EXPLOITER") {
            strategyData.exploiting_info = { exploits_player_group: "PokerX" };
        }
        const strategyPayload = JSON.stringify(strategyData);
        let response = await fetch(strategyUrl, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Authorization": "Bearer 8lXMmzymKafxZg1jxzi8mrof4WGDD9JH5HiuMwS4A9tQqrcdyBLKeBGuoPB07yo8"
            },
            body: strategyPayload
        });

        let response_data;
        let responseText = '';
        try {
            responseText = await response.text();
            response_data = JSON.parse(responseText);
        } catch (e) {
            loge(ws, "Response is not JSON", e.message, { response: responseText });
            throw new Error("Response is not JSON");
        }

        if (!response_data || !response_data.strategy || !response_data.strategy.actions) {
            throw new Error(`error calling strategy service (${response.status}): ${JSON.stringify(response_data)}`);
        }

        console.info({
            "timestamp": new Date().toISOString(),
            "roomId": ws.roomId,
            "userId": ws.userId,
            "msg": "glue service call",
            "url": strategyUrl,
            "payload": strategyPayload,
            "responseSource": response_data.solution_information.source,
            "responseActions": response_data.strategy.actions.reduce((acc, a) => {
                acc[a.action_name] = a.hand_strategy;
                return acc;
            }, {}),
            "suggestedAction": response_data.strategy.suggested_action,
            "gameuuid": room(ws).state.gameuuid,
        });
        // ws.log("strategy", response_data.solution_information.source, response_data.strategy.actions.map(a => ({
        //     "action": a.action,
        //     "action_name": a.action_name,
        //     "hand_strategy": a.hand_strategy,
        //     "hand_ev": a.hand_ev,
        // })), "\n");

        let userAction = body.userAciton || body;
        let canActionList = userAction.canActionList || [];
        let raiseScore = (userAction ? userAction.minRaiseScore : 2) || 2;

        let selectedAction = response_data.strategy.suggested_action;
        let action = STRATEGY_TO_ACTION[selectedAction.action];
        if (!canActionList.includes(action)) {
            const compatibleAction = {"BET": "RAISE", "RAISE": "CALL", "CALL": "CHECK"}[action]
            if (compatibleAction && canActionList.includes(compatibleAction)) {
                // console.warn(`Strategy action ${action} not in canActionList ${canActionList}, using ${compatibleAction} instead`);
                action = compatibleAction;
            } else if (["RAISE", "CALL"].includes(action)
                && canActionList.includes("ALL_IN")) {
                // console.info(`Strategy action ${action} not in canActionList ${canActionList}, using ALL_IN instead`);
                action = "ALL_IN";
            } else {
                throw new Error(`Strategy action is not in canActionList, got: ${action}, can: ${canActionList}`)
            }
        }

        if (!canActionList.includes(action)) {
            throw new Error(`Action ${action} not in canActionList ${canActionList}`);
        }

        if (!ACTION_TO_MESSAGE[action]) {
            throw new Error(`Unknown action ${selectedAction.action} from strategy service`);
        }

        if (action == "RAISE") {
            if (selectedAction.amount) {
                raiseScore = selectedAction.amount;
            } else if (selectedAction.pot_size) {
                let amount = Math.round(selectedAction.pot_size * room(ws).totalPot);
                // log if raiseScore is NaN
                if (isNaN(amount)) {
                    logw(ws, "raiseScore is NaN", {action: action, potSize: room(ws).totalPot, userAction: userAction});
                } else {
                    raiseScore = amount
                }
            } else {
                logw(ws, "No raise amount specified", {action: action, raiseScore: raiseScore})
            }
            if (raiseScore < body["minRaiseScore"]) {
                logw(ws, `Raise amount too low: ${raiseScore} < ${body["minRaiseScore"]}`,
                    {action: action, selectedAction: selectedAction, userAction: userAction, potSize: room(ws).totalPot});
                raiseScore = body["minRaiseScore"];
            } else if (raiseScore > body["maxRaiseScore"]) {
                logw(ws, `Raise amount too high: ${raiseScore} > ${body["maxRaiseScore"]}`,
                    {action: action, selectedAction: selectedAction, userAction: userAction, potSize: room(ws).totalPot});
                raiseScore = body["maxRaiseScore"];
            }
        }

        if (action == "FOLD" && canActionList.includes("CHECK")) {
            action = "CHECK";
        }

        logi(ws, "strategy action", {action: action, raiseScore: raiseScore, source: response_data.solution_information.source});
        // console.info("<<<", JSON.stringify(room(ws).state));
        // if (action == "ALL_IN") {
        //     console.info("vvv");
        //     console.info(JSON.stringify(room(ws).state))
        //     console.info(JSON.stringify(body))
        //     let strategyActions = response_data.strategy.actions.map(a => ({
        //         "action": a.action.action,
        //         "amount": a.action.amount,
        //         "hand_strategy": a.hand_strategy,
        //         "name": a.name
        //     }));
        //     console.info(strategyActions)
        //     console.info("^^^");
        // }

        let opt = ACTION_TO_MESSAGE[action]
        let data = "RAISE" == action ? {raiseScore: raiseScore} : null;
        let robotOptDelay = parseInt(RobotConst.roomCfg.robotOptDelay || 0) * 1000;
        robotOptDelay = robotOptDelay < 0 ? 0 : robotOptDelay;
        // 如果是发牌后的第1人操作,延时2秒操作等发牌动画播完
        if (new Date().getTime() - dealTime < 2000 && robotOptDelay < 2000) {
            robotOptDelay = 2000;
        }
        roomService.delayOpt(() => {
            setTimeout(() => ws.send(opt, data), Utils.isCD("strategyOpt" + ws.userId, 1) ? 1000 : 0);
        }, 500 + robotOptDelay, 1500 + robotOptDelay);
    }

    fallbackOpt(ws, msgType, body) {
        let dealTime = room(ws).dealTime || 0;
        let userAction = body.userAciton || body;
        let canActionList = userAction.canActionList || [];
        let raiseScore = (userAction ? userAction.minRaiseScore : 2) || 2;

        let action = "FOLD";
        if (canActionList.includes("CHECK")) {
            action = "CHECK";
        // } else {
        //     let totalPot = room(ws).totalPot || 1;
        //     let numOfPlayers = room(ws).state.players.length || 2;
        //     let winProb = winProbabilities(userAction.handCards, this.getPubCard(ws), numOfPlayers);
        //     // console.info(ws.userId, `Win probability: ${winProb}`);
        //
        //     if (canActionList.includes("CALL") && userAction.callScore / (totalPot + userAction.callScore) < winProb) {
        //         // console.info(ws.userId, `Call odds: ${userAction.callScore / (totalPot + userAction.callScore)}`);
        //         action = "CALL";
        //     } else if (canActionList.includes("RAISE") && raiseScore / (totalPot + raiseScore) < winProb) {
        //         // console.info(ws.userId, `Raise odds: ${raiseScore / (totalPot + raiseScore)}`);
        //         action = "RAISE";
        //     }
        }

        logi(ws, "fallback action", {action: action, raiseScore: raiseScore});
        // console.info(ws.userId, `Can actions: ${canActionList}`);

        let opt = ACTION_TO_MESSAGE[action];
        let data = "WP_raise" == opt ? {raiseScore: raiseScore} : null;
        let robotOptDelay = parseInt(RobotConst.roomCfg.robotOptDelay || 0) * 1000;
        robotOptDelay = robotOptDelay < 0 ? 0 : robotOptDelay;
        if (new Date().getTime() - dealTime < 2000 && robotOptDelay < 2000) {
            robotOptDelay = 2000;
        }
        roomService.delayOpt(() => {
            setTimeout(() => ws.send(opt, data), Utils.isCD("fallbackOpt" + ws.userId, 1) ? 1000 : 0);
        }, 500 + robotOptDelay, 1500 + robotOptDelay);
    }

    // 用户随机操作
    randomOpt(ws, msgType, body) {
        let userAction = body.userAciton || body, canActionList = userAction.canActionList || [], rInfo = room(ws), dealTime = rInfo.dealTime || 0;
        if (U.isEmpty(canActionList) || ws.userId != userAction.userId) {
            return;
        }
        // console.log(msgType, body);
        // console.log(JSON.stringify(rInfo.state));
        let opt, prob = U.random(1, 100), moreAction = U.filter(canActionList, a => U.contains(["CHECK", "CALL", "RAISE"], a));
        let smartRobot = RobotConst.roomCfg.poolingRate == "1", raiseScore = (userAction ? userAction.minRaiseScore : 2) || 2;
        let hasCheck = U.contains(canActionList, "CHECK"), handCards = userAction.handCards;
        let getCallOpt = _ => ({"CHECK": "WP_check", "CALL": "WP_call"}[U.sample(moreAction.filter(a => a != "RAISE"))]);
        let getCallOrRaiseOpt = _ => ({"CHECK": "WP_check", "CALL": "WP_call", "RAISE": "WP_raise"}[U.sample(moreAction)]);
        let isPreFlop = this.getRoomRound(ws) == 0; // 是否是翻牌前
        let preFlopPower = roomUserService.getCardPower(handCards); // 翻牌前牌力(不纳入公牌)
        // 如果翻牌前牌力差,5%随机的进行弃牌站起
        // if (preFlopPower > 5 && roomService.exitRoomOpt(ws, prob)) {
        //     return;
        // }
        // poolingRate 开启后, 不入池直接弃牌
        if (smartRobot && isPreFlop && preFlopPower > 5 && U.contains(canActionList, "FOLD")) {
            opt = "WP_fold";
        } else {
            let cardPower = roomUserService.getCardPower(handCards, this.getPubCard(ws.roomId));
            // 翻牌前操作
            if (isPreFlop) {
                let raiseMul = roomUserService.getPreFlopRaiseMultiple(cardPower), bb = RobotConst.roomCfg.BB || 2;
                if (raiseMul > 0 && rInfo.totalPot < U.random(10, 60) * bb) {
                    raiseScore = bb * raiseMul < rInfo.totalPot * 2 ? rInfo.totalPot * 2 : bb * raiseMul;
                    opt = "WP_raise";
                }
                opt = opt || getCallOpt();
            } else if (!U.isEmpty(moreAction)) { // 如果是强牌 或 随机概率 触发多的操作
                if (cardPower <= 13 || prob > (smartRobot ? 75 : 30)) {
                    opt = getCallOrRaiseOpt();
                }
                // 聪明机器人,根据牌力raise
                if (smartRobot && "WP_raise" == opt && raiseScore < rInfo.totalPot * 0.5) {
                    let range = 100;
                    if (cardPower <= 5) {
                        range = 250;
                    } else if (cardPower <= 8) {
                        range = 150;
                    }
                    if (cardPower == 99) { // 如果是空气牌,raise 转成 check或call
                        opt = getCallOpt();
                    }
                    raiseScore = rInfo.totalPot * (U.random(50, range) / 100);
                }
            }
            // 有牌力,无操作时,说明积分不够只能allin
            opt = !opt && cardPower <= 13 ? "WP_allin" : opt;
            // 最后还是无操操作则给予默认操作
            opt = opt || (prob > 95 && U.contains(canActionList, "ALL_IN") ? "WP_allin" : "WP_fold");
        }
        // 可以过牌的话,不进行弃牌.
        opt = opt == "WP_fold" && hasCheck ? "WP_check" : opt;

        // 加注操作 参数
        let data = "WP_raise" == opt ? {raiseScore: raiseScore} : null;
        let robotOptDelay = parseInt(RobotConst.roomCfg.robotOptDelay || 0) * 1000;
        robotOptDelay = robotOptDelay < 0 ? 0 : robotOptDelay;
        // 如果是发牌后的第1人操作,延时2秒操作等发牌动画播完
        if (new Date().getTime() - dealTime < 2000 && robotOptDelay < 2000) {
            robotOptDelay = 2000;
        }
        roomService.delayOpt(() => {
            setTimeout(() => ws.send(opt, data), Utils.isCD("randomOpt" + ws.userId, 1) ? 1000 : 0);
        }, 500 + robotOptDelay, 1500 + robotOptDelay);
    }

    onCloseRoomNotify(ws, msgType, body) {
        console.info("Should disconnect!")
        ws.disconnect();
    }

    // 获得房间当前公牌
    getPubCard(ws) {
        let pubCard = []
        Object.values(room(ws).publicCards || {}).forEach(d => (pubCard = pubCard.concat(d)));
        return pubCard;
    }

    // 获得房间当前流程 0:翻牌前,1:翻牌,2:转牌,3:河牌
    getRoomRound(ws){
        return Object.keys(room(ws).publicCards || {}).length;
    }

}

module.exports = new MsgReceive();
