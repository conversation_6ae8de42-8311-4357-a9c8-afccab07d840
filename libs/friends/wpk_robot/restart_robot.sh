# 杀掉某名字的进程
killProce(){
    while true
    do
        pid=`ps -ef |grep "$1" |grep -v "grep" | tail -n 1 |awk '{print $2}'`
        if [ $pid ]; then
            kill -9 $pid
            echo "killProce success : $1 PID:$pid "
        else
            break;
        fi
    done
}

killProce wpkRobotAppMain

# nodejs版本:  https://nodejs.org/dist/v16.18.1/node-v16.18.1-linux-x64.tar.gz
# >/dev/null
nohup node wpkRobotAppMain.js test >/dev/null 2>&1&

echo "start robot...done."