const Utils = require("../commoms/utils/nodeCommonUtils");
const U = Utils.U

class MsgSend {
    constructor(ws) {
        this.ws = ws;
        this.userId = ws.userId;
    }

    joinRoomAndSit(seatNum, cb) {
        let ws = this.ws;
        this.ws.send("C_joinRoom", null, (resp, t, body) => {
            if (!body) {
                return Utils.log(ws.userId, "C_joinRoom", resp);
            }
            Utils.log(`Join Room Successful : ${ws.userId} roomNumber:${body.roomNumber}, roomId:${body.roomId}`);
            this.sit(body, seatNum, cb);
        })
    }

    // 加入房间
    joinRoom(cb) {
        let ws = this.ws;
        this.ws.send("C_joinRoom", null, (resp, t, body) => {
            if (!body) {
                return Utils.log(ws.userId, "C_joinRoom", resp);
            }
            Utils.log(`加入房间成功 : ${ws.userId} roomNumber:${body.roomNumber}, roomId:${body.roomId}`);
            cb && cb(body);
            this.sit(body);
        })
    }
    // 坐下操作
    sit(room, seatNum, cb){
        if (!room || !room.gamePersonNum || room.gamePersonNum == 0 ) {
            return;
        }
        let existSeatNum = U.map(room.sitUserList, u => u.seatNum), seatArr = U.range(1, room.gamePersonNum + 1);
        if (U.size(existSeatNum) < room.gamePersonNum) {
            let rSeatNum = seatNum || U.sample(U.filter(seatArr, s => !U.contains(existSeatNum, s)));
            this.ws.send("C_sit", {seatNum: rSeatNum}, cb);
        }
    }
    // 弃牌
    fold(cb){
        this.ws.send("WP_fold", null, cb);
    }
    // 退出房间
    exitRoom(cb) {
        this.ws.send("C_exitRoom", null, cb)
    }
}

module.exports = MsgSend;