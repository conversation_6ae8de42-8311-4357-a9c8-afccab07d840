const Utils = require("../commoms/utils/nodeCommonUtils");
const EncryptUtils = require("../commoms/utils/encryptUtils");
const RobotConst = require("./common/wpkRobotConst");
const querystring = require("querystring");
const U = Utils.U

class RobotHttpReq {
    // http 请求封装
    static wpkRequest(userId, uri, param, cb) {
        userId = userId || RobotConst.robotUserList[0].userId;
        let user = RobotHttpReq.getUser(userId);
        if (user == null) {
            return Utils.log(`${userId} 用户为空`);
        }
        // 请求回调
        let reqCB = resp => {
            if (resp && U.contains([2000, 2001], resp.errorCode)) { // 登录失败重新登录
                return RobotHttpReq.wpkLogin(user.n, user.p, _ => req());
            }
            cb(resp)
        }
        let req = () => {
            let url = `${RobotConst.cfg.wpkHttpURL}${uri}?userId=${userId}&sessionToken=${user.sessionToken}&version=5.7.26.26&lang=en&deviceType=1&deviceId=B18DC1D7CC3C41579561804B4D52CE9A&idfa=0&platform=unknown&channel=0`
            let signedUrl = RobotHttpReq.signUrl(`${url}&${querystring.stringify(param)}`);
            Utils.request({
                url: signedUrl, data: param, callback: (r, e) => {
                    if (!U.isObject(r) || e) {
                        Utils.log("error", userId, uri, r, e);
                        r = null;
                    }
                    reqCB(r, e);
                }
            });
        }
        req()
    }

    static signUrl(url) {
        // take all query params, sort them, and generate a signature
        const urlObj = new URL(url);
        const params = urlObj.searchParams;

        params.sort();

        let signParams = [];
        for (let [key, value] of params.entries()) {
            signParams.push(`${key}${value}`);
        }

        let signText = signParams.join(',');
        // Clear all non-alphanumeric characters
        signText = signText.replace(/[^\w]/g, '');
        // Add before and after 110
        signText = `110${signText}110`;

        let signature = EncryptUtils.MD5(signText).toString().toUpperCase();

        return url + `&sign=${signature}`;
    };

    // 获得用户信息
    static getUser = userId => U.first(RobotConst.robotUserList.filter(u => u.userId == userId));

    static userRASPubKey = null

    // 生成用户数据加密的 AES key
    static generateUserAESKey = function (cb) {
        let encryptAESKey = (rasPubKey) => {
            if (!rasPubKey) {
                return cb(null);
            }
            let aesKey = Utils.randomStr(6);
            RobotHttpReq.userRASPubKey = rasPubKey;
            cb(aesKey, EncryptUtils.encryptRSA(aesKey, rasPubKey));
        }
        if (RobotHttpReq.userRASPubKey == null) {
            RobotHttpReq.wpkRequest(0, "/system/getUserRASPubKey.anon", {}, r => encryptAESKey(r && r.data ? r.data : null));
        } else {
            encryptAESKey(RobotHttpReq.userRASPubKey);
        }
    }

    // 生成game数据加密的 AES key
    static generateGameAESKey = function (userId, roomId, cb) {
        let encryptAESKey = (rasPubKey) => {
            if (!rasPubKey) {
                return cb(null);
            }
            let aesKey = Utils.randomStr(6);
            cb(aesKey, EncryptUtils.encryptRSA(aesKey, rasPubKey));
        }
        RobotHttpReq.wpkRequest(userId, "/system/getGameRSAPubKey", {roomId}, r => encryptAESKey(r && r.data ? r.data : null));
    };

    // 登录
    static wpkLogin(username, pass, cb) {
        RobotHttpReq.generateUserAESKey((aesKey,enAesKey)=>{
            let url = `${RobotConst.cfg.wpkHttpURL}/user/phone_login?account=${username}&password=${pass}&time=*************&lang=en&isSimulator=false&countryCode=%2B86&deviceVersion=&phoneModel=safari&networkOper=&appVersionCode=1000000&version=5.7.26.26&signVersion=&packageName=&deviceType=1&deviceId=B18DC1D7CC3C41579561804B4D52CE9A&idfa=0&platform=unknown&channel=0&isAutoLogin=true`
            let signedUrl = RobotHttpReq.signUrl(url);
            Utils.request({
                url: signedUrl,type:"post",data:{aesKey: enAesKey},
                callback: r => {
                    if (!r || r.errorCode != 0) {
                        return Utils.log("error", `登录失败:`, username, r);
                    }
                    let user = RobotHttpReq.getUser(r.user.userId);
                    let logCB = (token) => {
                        user.aesKey = aesKey
                        user.sessionToken = r.sessionToken = token
                        let tokenMap = Utils.cache("tokenMap") || {};
                        tokenMap[user.userId] = token + "," + aesKey;
                        Utils.cache("tokenMap", tokenMap, 8 * 3600);
                        cb(r)
                    }
                    // 设备变更安全验证
                    if (r.sessionToken == "0") {
                        return RobotHttpReq.verifyDevice(r.user.userId, token => logCB(token));
                    }
                    logCB(r.sessionToken);
                }
            });
        });
    }

    // 登录
    static wpkLoginById(userId, cb) {
        let user = RobotHttpReq.getUser(userId);
        RobotHttpReq.wpkLogin(user.n, user.p, cb)
    }

    // 设备安全验证
    static verifyDevice(userId, cb) {
        RobotHttpReq.wpkRequest(userId, "/user/verify_update_device_security_code", {
            updateType: 1,
            code: 1234
        }, r => r && r.data && cb(r.data.token));
    }

    // 创建好友房
    static createRoom(userId, cb) {
        let param = [
            {"duration":30,"isNeedAgreeSit":0,"grade":1,"maxScoreMultiple":-1,"minScoreMultiple":1,"minBuyInScore":200,"maxBuyInScore":-1,"isAnte":1,"ante":1,"gameFund":0,"gameFundRate":0.0,"isSendDouble":0,"roomType":1,"isLimitIp":0,"gamePersonNum":8,"playType":0,"isLimitScore":0,"isThirdBlind":1,"isActSeeCard":0,"isRandSeat":0},
            {"duration":480,"isNeedAgreeSit":0,"grade":1,"maxScoreMultiple":-1,"minScoreMultiple":1,"minBuyInScore":200,"maxBuyInScore":-1,"isAnte":0,"ante":0,"gameFund":0,"gameFundRate":0.0,"isSendDouble":0,"roomType":1,"isLimitIp":0,"gamePersonNum":9,"playType":0,"isLimitScore":0,"isThirdBlind":0,"isActSeeCard":0,"isRandSeat":0},
            {"roomTitle":"暴风雪51的牌局","ante":2,"duration":480,"gameFund":0,"gameFundRate":0.0,"gamePersonNum":9,"grade":2,"isActSeeCard":0,"isAnte":0,"isLimitIp":0,"isLimitScore":0,"isNeedAgreeSit":0,"isRandSeat":0,"isSendDouble":0,"isThirdBlind":0,"maxBuyInScore":-1,"maxScoreMultiple":-1,"minBuyInScore":100,"minScoreMultiple":1,"playType":1,"roomType":1,"shortCompareType":1,"minKeepScore":0,"maxKeepScore":0},
            {"ante":0,"duration":480,"gameFund":0,"gameFundRate":0.0,"gamePersonNum":9,"grade":1,"isActSeeCard":0,"isAnte":0,"isLimitIp":0,"isLimitScore":1,"isNeedAgreeSit":0,"isRandSeat":0,"isSendDouble":0,"isThirdBlind":0,"maxBuyInScore":-1,"maxScoreMultiple":-1,"minBuyInScore":200,"minScoreMultiple":1,"playType":2,"roomType":1,}
        ]
        let roomCfg = RobotConst.roomCfg;
        let playType = roomCfg.playType == -1 ? U.sample([0, 0, 0, 0, 1, 2]) : roomCfg.playType;
        let data = U.extend(param[playType], roomCfg, {playType: playType});
        data.roomTitle = userId + "机器人的牌局";
        RobotHttpReq.wpkRequest(userId, "/battle/createRoom", data, r => cb(r, userId))
    }

    // 我的好友房列表
    static getRoomRecordByCreateView(userId, cb) {
        RobotHttpReq.wpkRequest(userId, "/hall/getRoomRecordByCreateView", {pageNum: 0}, r => r && cb(r, userId));
    }

    // 检查能否进入房间
    static checkUserIntoRoomByRoomId(userId,roomId, cb) {
        RobotHttpReq.wpkRequest(userId, "/battle/check_user_into_room_by_roomid", {"roomId":roomId}, r => r && cb(r, userId));
    }

    // 开发环境 调试 devTest.anon
    static systemDevTest(userId, param, cb) {
        RobotHttpReq.wpkRequest(userId, "/system/devTest.anon", param, r => r && cb(r, userId));
    }
}

module.exports = RobotHttpReq;