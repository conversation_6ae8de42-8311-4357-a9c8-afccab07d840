loginServer: function (parameters, type, isLoginAgain) {
    // Save login information
    const loginInfo = {
        parameters: parameters,
        type: type,
    };
    setStorage('Logininfo', loginInfo, true);
    cc.vv.riskVerify.rickChecked = false;
    HMFAppConfig.inRiskChecking = false; // Cancel risk control
    this.isError1074 = false;
    let tmpY = this.passEditBox.node.y - this.passEditBox.node.height / 2;
    this.showLoginLoadingView();

    if (this.isCanloginServer) {
        return;
    }

    this.isCanloginServer = true;

    if (!this.loginTimes) {
        this.loginTimes = 0;
    }

    if (this.loginTimes >= 3 && !isLoginAgain && this.isError1074) {
        // Retry login after risk checking
        this.CheckNet = new CheckNet({
            loginData: {
                loginServer: this.loginServer,
                loginParameters: parameters,
                loginType: type,
            },
        });
        this.CheckNet.mainTodo(0);
        this.loginTimes = 0;
        return;
    }
    this.loginTimes++;

    const self = this;

    let encryptedAesKey = DataEncoder.instance.getRSAEncryptedAESKey();
    parameters.aesKey = encryptedAesKey;

    if (type == 1) {
        // Guest login
        return HMFHTTPClient.login(parameters).then(function (data) {
            loginJs.setUserData(parameters, data, type);
        }, function (erro, data) {
            self.isCanloginServer = false;
            // Handle login error codes
            // ...
        });
    } else if (type == 2) {
        // Email login
        return HMFHTTPClient.emailLogin(parameters).then(function (data) {
            if (parameters.email) {
                HMFAppDatas.storeLoginEmail(parameters.email);
            }
            // Handle email login response
            // ...
        }, function (erro, data) {
            // Handle email login errors
            // ...
        });
    } else if (type == 3) {
        // Phone number login
        return HMFHTTPClient.phoneNumLogin(parameters).then(function (data) {
            if (parameters.phoneNum) {
                HMFAppDatas.storeLoginPhoneNum({ phoneNum: parameters.phoneNum, countryCode: parameters.countryCode });
            }
            // Handle phone number login response
            // ...
        }, function (erro, data) {
            // Handle phone number login errors
            // ...
        });
    } else if (type == 4) {
        // Account login
        return HMFHTTPClient.accountLogin(parameters).then(function (data) {
            if (parameters.account) {
                HMFAppDatas.storeLoginAccout(parameters.account);
            }
            // Handle account login response
            // ...
        }, function (erro, data) {
            // Handle account login errors
            // ...
        });
    }
},