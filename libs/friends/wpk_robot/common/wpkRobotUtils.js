const Utils = require("../../commoms/utils/nodeCommonUtils");
const protobuf = require("protobufjs");
const U = Utils.U;

class ProtoUtil {
    static protobufCache = {} // PB对象缓存

    // 加载 proto 文件
    static loadProtobuf(){
        let protoPath = "./config/proto/"
        U.each(["MsgDeliverReqProto.proto","MsgDeliverRespProto.proto"],f=>{
            protobuf.load(protoPath + f, function (err, root) {
                if (err) {
                    return Utils.log("proto 加载失败", err);
                }
                U.each(root.lookup("com.hm.wepoker.battle.controller.proto").nested, (v, k) => ProtoUtil.protobufCache[k] = v);
            })
        })
    }

    // 把pb 解析的值对象类型 转换为 基础类型,例: {age:{value:22}} => {age:2}
    static valObjConvert(obj) {
        (U.isObject(obj) || U.isArray(obj)) && U.each(obj, (v, k) => {
            let nVal = v && U.isObject(v) && U.size(v) == 1 && U.keys(v)[0] == "value" ? v.value : v;
            obj[k] = ProtoUtil.valObjConvert(nVal);
        })
        return obj
    }

    static encode(type, Obj) {
        let pType = ProtoUtil.protobufCache[type]
        return pType ? pType.encode(pType.create(Obj)).finish() : null;
    }

    static decode(type, buffer){
        let pType = ProtoUtil.protobufCache[type]
        let obj = pType ? pType.toObject(pType.decode(buffer),{enums:String,longs:String,defaults:true,arrays:true,objects:true,oneofs:true}) : null;
        return obj ? ProtoUtil.valObjConvert(obj) : obj;
    }
}

ProtoUtil.loadProtobuf();

class RobotUtils {
    static ProtoUtil = ProtoUtil
}

module.exports = RobotUtils;