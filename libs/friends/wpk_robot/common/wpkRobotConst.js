class RobotConst{
    // 消息类型 PB对象映射
    static msgTypeMap = {C_imMsg:"MsgTypeMsgPO",WP_raise:"BattleRaisePO",C_checkSit:"BattleSitMsg",C_checkSitV2:"BattleSitMsg",C_sit:"BattleSitMsg",WP_openCard:"BattleOpenCardReq",WP_autoOpt:"BattleAutoOptPO",WP_addScore:"BattleScorePO",WP_addScoreOtherPay:"BattleScorePO",WP_chooseDouble:"BattleDoubleChooseReq",C_emoji:"EmojiMsgPO",C_diamondSend:"DiamondSendMsgPO",WP_seeComCard:"ForceStandReq",C_forceStand:"ForceStandReq",WP_forceSeeCard:"LookComCardsReq",C_shieldUserMsg:"ShieldUserMsgReq",C_redPack:"OptTargetPB",WP_guessHand:"GuessHandBetPO",WP_retraceScore:"RetraceScorePO",WP_dealNotify:"BattleDealCardPOList",C_updateRoomNotify:"BattleRoomMsg",C_updateRoomUserNotify:"BattleRoomMsg",C_updateUserInfoNotify:"BattleRoomUserMsg",WP_bankerChangeNotify:"BattleBankChangeMsg",WP_actionNotify:"BattleActionMsg",WP_userOptNotify:"BattleUserOptMsg",WP_roundChangeNotify:"BattleRoundChangeMsg",WP_addScoreStatusNotify:"BattleAddScoreStatusResp",WP_playResultNotify:"BattleThanResp",WP_prolongNotify:"BattleProlongMsg",C_imMsgNotify:"ChatMsgPO",C_emojiNotify:"ChatMsgPO",C_diamondSendNotify:"DiamondRespMsgPO",WP_userChooseDoubleNotify:"BattleSendDoubleOptMsg",WP_userCSDResultNotify:"BattleCSDResultMsg",WP_openSendDoubleNotify:"AllinOpenCardMsg",WP_sendDoubleThanResultNotify:"BattleDoubleThanResultMsg",WP_showCardNotify:"BattleDealCardPO",WP_openCardNotify:"BattleOpenCardMsg",WP_openCardByAllinNotify:"AllinOpenCardMsg",AM_sendGameMsg:"MessageDoorPO",C_playLogNotify:"RoomPlayRecordOneHand",WP_seeComCardNotify:"LookComCardsMsg",C_updateRoomDetail:"BattleRoomDetailResp",WP_waitHandsNotify:"WaitHandResp",WP_raiseBlind:"RaiseBlindMsg",WP_forceSeeCardNotify:"ForceSeeCardResp",C_marqueeMsgNotify:"MarqueeMsgResp",C_request:"BaseResp",WP_updateUserProfileNotify:"BattleUserProfileResp",C_mttStartNotify:"MttStartMsg",C_starGame:"StarGameMsg",WP_updateDiamondBalanceNotify:"UpdateUserDiamondBalance",C_redPackNotify:"RedPackMsg",C_quickSportTicketsNotify:"QuickSportTicketsRespMsgPO",WP_deskSportNotify:"DeskSportNoticeMsg",C_updateUserBalance:"UpdateUserBalance",C_checkSitNotify:"BaseDataPO",WP_guessHandBetNotify:"BaseDataPO",WP_guessHandSettleNotify:"BaseDataPO",C_payMatchNotify:"PayMatchInfoPO",WP_retraceScoreNotify:"RetraceScoreMsgPO",}
    // 机器人用户列表 [{userId: 34567002, n: "username"}]
    static robotUserList = []
    // 用户分组(分房间)
    static userGroup = []
    // 机器人聊天消息
    static robotImMsg = []

    // **************************************
    // 机器人服务Http端口
    static HTTP_SERVER_PORT = "8890";
    // 机器人登录的密码
    static loginPassword = "03F869A5FE21C5F8CA684212F7950514"
    // 配置文件 config/robotConfig.json
    static cfg = {
        wpkHttpURL: "",       // wpk hall Http 请求
        wpkGameWsURL: "",     // wpk game WS 请求
        robotUserCsvPath: "", // 机器人信息配置路径
        printLogUserId: "",    // 输出WS 发送/接受 日志的用户ID,空则都输出
        serverPort: "",
        strategyServices: ["RL"],
	hints: [],
    }
    static roomCfg = {playType:-1, poolingRate: "1"}     // 创建房间配置
}

module.exports = RobotConst;
