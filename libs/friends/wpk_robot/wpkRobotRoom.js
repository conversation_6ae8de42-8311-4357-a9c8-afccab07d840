const Utils = require("../commoms/utils/nodeCommonUtils");
const RobotWSConnect = require("./wpkRobotWSConnect");

const U = Utils.U;

class RobotRoom {
    // 是否可以开始游戏
    isCanStartGame = room => room.isAdmin == 1 && room.roomStatus == 3 && U.size(room.sitUserList) > 1;

    // 延时操作
    delayOpt(cb, minTime, maxTime) {
        setTimeout(() => cb(), U.random(minTime, maxTime || (minTime + 500)));
    }

    // 退出房间操作
    exitRoomOpt(ws, prob) {
        if (prob > 5) {
            return false;
        }
        let self = this;
        let reCon = ()=>{
            ws.ws.close();
            self.delayOpt(_ => ws.reconnect(),3000,8000)
        }
        self.delayOpt(() => ws.M.fold(() => ws.M.exitRoom(reCon)), 900)
        return true;
    }
}


module.exports = new RobotRoom();