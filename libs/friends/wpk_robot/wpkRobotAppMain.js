const Utils = require("../commoms/utils/nodeCommonUtils");
const wpk = require("./wpkRobotWSConnect");
const RobotHttpReq = require("./wpkRobotHttpReq");
const RobotConst = require("./common/wpkRobotConst");
const admin = require("./wpkRobotAdmin");

const U = Utils.U

// 最后启动时间
let lastStartRobotTime = 0;

class RobotMain {
    // 机器人进入房间
    static robotJoinRoomAndSit(userId, roomId, seatNum, callback) {
        RobotHttpReq.checkUserIntoRoomByRoomId(userId, roomId, (resp, userId) => {
            RobotHttpReq.generateGameAESKey(userId, roomId, (aesKey, enAesKey) => {
                let user = RobotHttpReq.getUser(userId);
                user.gameAesKey = aesKey;
                let urlPath = resp.urlPath;
                if (urlPath) {
                    wpk.wsConnect(urlPath, userId, roomId, enAesKey, ws => ws.M.joinRoomAndSit(seatNum, callback));
                } else {
                    console.error(resp);
                }
            });
        });
    }

    static robotJoinRoom(userId, defRoomId, callback) {
        let roomCB = roomId => RobotHttpReq.checkUserIntoRoomByRoomId(userId, roomId, (resp, userId) => {
            RobotHttpReq.generateGameAESKey(userId, roomId, (aesKey, enAesKey) => {
                let user = RobotHttpReq.getUser(userId);
                user.gameAesKey = aesKey;
                let urlPath = resp.urlPath;
                wpk.wsConnect(urlPath, userId, roomId, enAesKey, ws => ws.M.joinRoom(callback));
            });
        });
        RobotHttpReq.getRoomRecordByCreateView(userId, (resp, userId) => {
            if (defRoomId) {
                return roomCB(defRoomId);
            }
            let room = U.first(resp.roomList);
            if (!room) {
                RobotHttpReq.createRoom(userId, r => roomCB(r.roomId));
            } else {
                roomCB(room.roomId);
            }
        })
    }

    // 初始化机器人配置
    static initRobotConfig() {
        RobotConst.cfg = JSON.parse(Utils.readFile(Utils.curPath() + `/config/robotConfig.json`))[process.argv[2]];
        RobotConst.robotImMsg = Utils.readFile(Utils.curPath() + `/config/imMsg.txt`).split("\n");
        if (!RobotConst.cfg.strategyServices || !Array.isArray(RobotConst.cfg.strategyServices) || RobotConst.cfg.strategyServices.length === 0) {
            RobotConst.cfg.strategyServices = ["RL"];
        }
    }

    // 初始化机器人信息
    static initRobotUserInfo(robotNum) {
        let robotCsv = Utils.readFile(Utils.curPath() + `/${RobotConst.cfg.robotUserCsvPath}`), groupIdx = 0, groupSize = 0,
            uGroup = RobotConst.userGroup;
        let lines = robotCsv.split("\n"), randomList = Utils.seededRandom(9, lines.length / 5, 5, 15); // 每组 5 ~ 15人
        let tokenMap = Utils.cache("tokenMap") || {};
        lines = U.filter(lines, u => u).slice(0, robotNum);
        U.each(lines, u => {
            let arr = u.split(","), userId = parseInt(arr[0]), uToken = (tokenMap[userId] || "0,0").split(","),
                user = {userId: userId, n: arr[1], sessionToken: uToken[0], aesKey: uToken[1], p: arr.length > 2 ? arr[2]: RobotConst.loginPassword};
            RobotConst.robotUserList.push(user);
            // 用户分组
            if (groupIdx++ == 0) {
                groupSize = randomList[uGroup.length]
                uGroup.push({userIds: [], roomId: 0});
            }
            groupIdx = groupIdx >= groupSize ? 0 : groupIdx;
            U.last(uGroup).userIds.push(userId);
        });
    }

    static initRobotUsersInfo() {
        let robotCsv = Utils.readFile(Utils.curPath() + `/${RobotConst.cfg.robotUserCsvPath}`), groupIdx = 0, groupSize = 0,
            uGroup = RobotConst.userGroup;
        let lines = robotCsv.split("\n"), randomList = Utils.seededRandom(9, lines.length / 5, 5, 15); // 每组 5 ~ 15人
        let tokenMap = Utils.cache("tokenMap") || {};
        lines = U.filter(lines, u => u);
        U.each(lines, u => {
            const arr = u.split(",");
            const userId = parseInt(arr[0]);
            const uToken = (tokenMap[userId] || "0,0").split(",");
            const user = {userId: userId, n: arr[1], sessionToken: uToken[0], aesKey: uToken[1], p: arr.length > 2 ? arr[2]: RobotConst.loginPassword};
            RobotConst.robotUserList.push(user);
        });
    }


    // 启动
    static startRobot(robotNum, roomId=0) {
        lastStartRobotTime = U.now();
        RobotMain.initRobotUserInfo(robotNum);
        Utils.eachInterval(RobotConst.userGroup, (group, idx) => {
            // 分组的第一个用户当房主
            RobotMain.robotJoinRoom(U.first(group.userIds), roomId, room => {
                if (group.joinComplete) {
                    return;
                }
                Utils.log(`********************* 分组(${idx}),进入房间(${room.roomId}) 房间号:${room.roomNumber}`, JSON.stringify(group.userIds));
                group.roomId = room.roomId;
                group.roomNumber = room.roomNumber;
                group.playType = room.playType;
                U.each(group.userIds, (id, idx2) => {
                    (idx2 > 0) && setTimeout(() => RobotMain.robotJoinRoom(id, room.roomId), 500 / U.size(group.userIds), U.random(1, 1000));
                });
                group.joinComplete = true;
            })
        }, 200);
    }

    static joinRoom(roomId, userId, seatNum) {
        wpk.wsExit(userId, -1);
        RobotMain.robotJoinRoomAndSit(userId, roomId, seatNum, (resp) => {
            if (resp && resp.errorCode == 7007) {
                wpk.wsExit(userId, roomId);
            }
        });
    }

    static exitRoom(userId, roomId) {
        wpk.wsExit(userId, roomId);
    }

    static state() {
        return wpk.state();
    }

    // 停止
    static stopRobot() {
        let stopRoom = () => {
            const group = U.first(RobotConst.userGroup)
            if (group != null) {
                wpk.wsDisconnect(group.roomId)
                RobotConst.userGroup.splice(0, 1);
                stopRoom();
            }
        }
        stopRoom()
    }

    static startInterval() {
        setInterval(() => {
            // let userId = RobotConst.robotUserList[0].userId;
            // 跑马灯消息测试
            // RobotHttpReq.systemDevTest(34567001, {param: "testMarqueeMsg"}, resp => console.log(resp));
            // 启动超过8小时自动停止
            if (U.now() - lastStartRobotTime > 8 * 60 * 60000) {
                RobotMain.stopRobot()
            }
        }, 60000);
    }

    static main() {
        if (process.argv.length < 3) {
            Utils.log("Usage: node wpkRobotAppMain.js env");
            return;
        }

        RobotMain.initRobotConfig();
        // RobotMain.startInterval();
        admin.startHttpServer(RobotMain);

        RobotMain.initRobotUsersInfo();
        // RobotMain.startRobot(parseInt(process.argv[3]), process.argv.length > 4 ? process.argv[4] : 0);
    }
}

RobotMain.main();
