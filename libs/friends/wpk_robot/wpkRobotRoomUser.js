const Utils = require("../commoms/utils/nodeCommonUtils");
const RobotConst = require("./common/wpkRobotConst");
const U = Utils.U;


// c2c:牌转换成花色编码, c2n:牌转换成牌号
let c2c = c => Array.isArray(c) ? c.map(d => parseInt(d / 100)) : parseInt(c / 100), c2n = c => Array.isArray(c) ? c.map(d => d % 100) : c % 100;
// 为了方便计算,A 牌号转换成 14,然后从大到小排序,取前2张
let c2Card = card => JSON.parse(JSON.stringify(card)).map(c => c2n(c) == 1 ? parseInt((c + "").substring(0, 1) + "14") : c).sort((a, b) => c2n(b) - c2n(a));


// 获得翻前牌力(长牌)
function getPreFlopCardPower(card) {
    // 不符合规则默认:牌力99
    if (!card || card.length < 2) {
        return 99;
    }
    card = c2Card(card).slice(0, 2); // 为了方便计算,A 牌号转换成 14,然后从大到小排序,取前2张
    let numArr = c2n(card), num1 = numArr[0], num2 = numArr[1];// 牌号
    // 是否同色
    let isSuited = c2c(card[0]) == c2c(card[1]);
    // hasAce:是否有A, isPairs:是否是对子
    let hasAce = num1 == 14, isPairs = num1 == num2;
    // 1等牌力:AA,KK,AK,QQ
    if (numArr.filter(n => n >= 13).length > 1 || (isPairs && num1 == 12)) {
        return 1;
    }
    // 2等牌力:都Q以上 或 对子10以上 或 同色AJ,A10,A5
    if (numArr.filter(n => n >= 12).length > 1 || (isPairs && num1 >= 10) || (isSuited && hasAce && (num2 >= 10 || num2 == 5))) {
        return 2;
    }
    // 3等牌力:都J以上 或 对子8以上 或 同色带A 或 同色10以上
    if (numArr.filter(n => n >= 11).length > 1 || (isPairs && num1 >= 8) || (isSuited && hasAce) || (isSuited && num2 >= 10)) {
        return 3;
    }
    // 4等牌力: (同色 都9以上 或 5以上同色连张) 或 (杂色:10以上) 或 (对子6以上)
    if ((isSuited && (num2 >= 9 || (num1 - 1 == num2 && num2 >= 5))) || num2 >= 10 || (isPairs && num2 >= 6)) {
        return 4;
    }
    // 5等牌力: (同色 带K 或 连张) 或 对子
    if ((isSuited && (num1 == 13 || num1 - 1 == num2)) || isPairs) {
        return 5;
    }
    return 99;
}

// 获得牌力 (十位个位:  1:A, 2 ~ 10, 11:J, 12:Q, 13:K     百位:     1:黑桃, 2:红桃, 3:梅花, 4:方块)
function getCardPower(card, pubCard) {
    if (!card || !card.length) {
        return;
    }
    pubCard = c2Card(pubCard || []);
    if (pubCard.length < 3) {
        return getPreFlopCardPower(card);
    }
    card = c2Card(card).slice(0, 2); // 为了方便计算,A 牌号转换成 14,然后从大到小排序,取前2张
    let allCard = c2Card(card.concat(pubCard)), nMap = {}, cMap = {};
    let cardColor = c2c(card), numArr = c2n(card), num1 = numArr[0], num2 = numArr[1];// 手牌花色 与 牌号
    let nArr = c2n(allCard), cArr = c2c(allCard) // nArr:牌号, cArr:花色
    nArr.forEach(n => nMap[n + ""] = (nMap[n + ""] || 0) + 1); // 收集牌号出现次数
    cArr.forEach(c => cMap[c + ""] = (cMap[c + ""] || 0) + 1); // 收集花色出现次数
    let cmArr = Object.entries(cMap), nmArr = Object.entries(nMap); // 牌号/花色出现次数,转换成数组
    let cardPairs = num1 == num2; // 手牌是否是对子
    let isStraight = false, isFlush = false;
    // 是否顺子
    for (let i = 0; i < nArr.length - 3; i++) {
        if (nArr[i] - 1 == nArr[i + 1] && nArr[i + 1] - 1 == nArr[i + 2] && nArr[i + 2] - 1 == nArr[i + 3]
            && (nArr[i + 3] - 1 == nArr[i + 4] || (nArr[i + 3] == 2 && nArr[0] == 14))) {
            isStraight = true;
        }
    }
    // 是否同花 (手牌里需要有同花听牌的花色)
    let c5Arr = cmArr.filter(d => d[1] >= 5);
    if (c5Arr.length && cardColor.filter(c => c == c5Arr[0][0]).length) {
        isFlush = true;
    }
    // 1:同花顺
    if (isFlush && isStraight) {
        return 1;
    }
    let pArr = nmArr.filter(d => d[1] >= 2); // 对子/2对/三条/四条
    // 手牌在其中的,对子/2对/三条/四条
    let cpArr = pArr.filter(d => card.map(c => c2n(c)).filter(c => c == d[0]).length); // 手牌在其中的对子以上

    // 2:4条
    if (cpArr.filter(d => d[1] >= 4).length) {
        return 2;
    }
    // 3:葫芦1(自己需要是3条里面的而不是对子里的)
    let threeKind = cpArr.filter(d => d[1] >= 3).length; // 是否三条(自己手牌参与的3张同点数)
    if (threeKind && pArr.length >= 2) {
        return 3;
    }
    // 4:葫芦2(自己是对子里的葫芦)
    let threeKind2 = pArr.filter(d => d[1] >= 3)
    if (threeKind2.length && cpArr.length && cpArr.filter(c => c[0] != threeKind2[0][0]).length) {
        return 4;
    }
    // 5/6:同花
    if (isFlush) {
        return card.filter(c => c2c(c) == c5Arr[0][0] && c2n(c) >= 12).length ? 5 : 6; // Q及以上为大同花
    }
    // 7:顺子
    if (isStraight) {
        return 7;
    }
    // 8:暗3条
    if (threeKind && cardPairs) {
        return 8;
    }
    // 9:明3条
    if (threeKind) {
        return 9;
    }
    // 10:2对
    if (cpArr.length >= 2) {
        return 10;
    }
    // 11:超对
    if (cardPairs && num1 > c2n(pubCard[0])) {
        return 11;
    }
    // 12:顶对
    if (cpArr.filter(d => d[0] == nArr[0]).length) {
        return 12;
    }
    // 13:河牌之前 强听牌
    if (pubCard.length <= 4) {
        // 是否顺子听牌
        for (let i = 0; i < nArr.length - 3; i++) {
            if (nArr[i] - 1 == nArr[i + 1] && nArr[i + 1] - 1 == nArr[i + 2] && nArr[i + 2] - 1 == nArr[i + 3]) {
                return 13;
            }
        }
        // 是否同花听牌 (手牌里需要有同花听牌的花色)
        let c4Arr = cmArr.filter(d => d[1] >= 4);
        if (c4Arr.length && cardColor.filter(c => c == c4Arr[0][0]).length) {
            return 13;
        }
    }
    return 99;
}


class RobotRoomUser {
    // 获得房间用户
    getRoomUser(room, userId) {
        let uList = U.isArray(room) ? room : [[], room.sitUserList, room.lookList, room.buyScoreList], allUser = [];
        U.each(uList, list => (allUser = allUser.concat(list || [])));
        return U.find(allUser, u => u.userId == userId);
    }

    // 获得牌力, 手牌是否入池,翻前15%左右范围 (十位个位:  1:A, 2 ~ 10, 11:J, 12:Q, 13:K     百位:     1:黑桃, 2:红桃, 3:梅花, 4:方块)
    getCardPower(card, pubCard) {
        return getCardPower(card, pubCard)
    }

    // 获得翻前,加注范围(大盲倍数)
    getPreFlopRaiseMultiple(cardPower) {
        let range = [[10, 30], [5, 20], [1, 10]][cardPower - 1];
        return range ? U.random(range[0], range[1]) : 0;
    }
}

module.exports = new RobotRoomUser();

function showCard(card) {
    let convert = c => ["♠", "♥", "♣", "♦"][parseInt(c / 100) - 1] + ({"1": "A", "11": "J", "12": "Q", "13": "K"}[c % 100] || c % 100);
    return Array.isArray(card) ? JSON.stringify(card.map(c => convert(c))) : convert(card);
}

function testGetCardPower() {
    // 生成打乱的排堆
    let generateCardArr = _ => {
        let cardArr = [];
        for (let c = 1; c <= 4; c++) {
            for (let n = 1; n <= 13; n++) {
                cardArr.push(c * 100 + n);
            }
        }
        for (let i = cardArr.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [cardArr[i], cardArr[j]] = [cardArr[j], cardArr[i]];
        }
        return cardArr;
    }

    for (let i = 0; i < 500; i++) {
        let cardArr = generateCardArr(), info = "";
        info += i;
        for (let n = 3; n <= 5; n++) {
            let card = cardArr.splice(-2), pubCard = cardArr.splice(n * -1);
            let power = getCardPower(card, pubCard);

            info += `  |  ${(power + "").padStart(2, ' ')} 手牌:${showCard(card).padStart(13, ' ')} 公牌:${showCard(pubCard).padStart(pubCard.length * 6, ' ')}`;
        }
        console.log(info);
    }
}

function testGetCardPower2() {
    function convertCard(card) {
        let convert = c => ({"♠": 1, "♥": 2, "♣": 3, "♦": 4}[c.substring(0, 1)] * 100) + parseInt({"A": "1", "J": "11", "Q": "12", "K": "13"}[c.substring(1)] || c.substring(1));
        return Array.isArray(card) ? card.map(c => convert(c)) : convert(c);
    }

    // 测试
    let testData = [
        // [["♠A","♣A"], []],
        // [["♠K","♣K"], []],
        // [["♠A","♣K"], []],
        // [["♠A","♠K"], []],
        // [["♠K","♠8"], []],
        [["♠K", "♠10"], ["♠Q", "♠J", "♠9", "♥4", "♣5"]], // 同花顺
        [["♠K", "♥K"], ["♣K", "♠9", "♥4", "♣5", "♦K"]],  // 4条
        [["♠K", "♥K"], ["♣K", "♠9", "♥4", "♣5", "♦5"]],  // 葫芦1
        [["♠K", "♠9"], ["♣K", "♥K", "♥4", "♣5", "♦5"]],  // 葫芦1
        [["♣5", "♠9"], ["♣K", "♥K", "♠K", "♥4", "♦5"]],  // 葫芦2
        [["♣5", "♠9"], ["♣K", "♥K", "♠K", "♥4", "♦4"]],  // 公葫芦
        [["♠K", "♠10"], ["♠Q", "♠2", "♠9", "♥4", "♣5"]], // 大同花
        [["♠8", "♠10"], ["♠Q", "♠2", "♠9", "♥4", "♣5"]], // 小同花
        [["♠K", "♠10"], ["♠Q", "♥J", "♠9", "♥4", "♣5"]], // 顺子
        [["♠2", "♠3"], ["♠Q", "♥J", "♠6", "♥4", "♣5"]],  // 顺子
        [["♠A", "♠2"], ["♠Q", "♥J", "♠3", "♥4", "♣5"]],  // A顺子
        [["♠K", "♦K"], ["♣K", "♠9", "♥4", "♣5", "♥A"]],  // 暗3条
        [["♠K", "♥A"], ["♣K", "♠9", "♥4", "♣5", "♦K"]],  // 明3条
        [["♠K", "♥A"], ["♣K", "♠9", "♥4", "♣5", "♦A"]],  // 2对
        [["♠A", "♥A"], ["♣K", "♠9", "♥4", "♣5", "♦7"]],  // 超对
        [["♠A", "♥2"], ["♣K", "♠9", "♥4", "♣5", "♦A"]],  // 顶对
        [["♠3", "♥2"], ["♣K", "♠9", "♥4", "♣5"]],  // 顺子听牌
        [["♥3", "♥2"], ["♣K", "♥9", "♥Q", "♣5"]],  // 同花听牌
        [["♥3", "♠2"], ["♣K", "♥9", "♥Q", "♣5"]],  // 杂牌

    ];
    testData.forEach((d, idx) => console.log(`${idx} 手牌:${d[0]} 公牌:${(d[1] + "").padStart(16, ' ')} --> ${getCardPower(convertCard(d[0]), convertCard(d[1]))}`));
}

// testGetCardPower2()

