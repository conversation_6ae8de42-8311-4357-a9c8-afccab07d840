const Utils = require("../commoms/utils/nodeCommonUtils");
const encryptUtils = require("../commoms/utils/encryptUtils");
const webSocketUtils = require("../commoms/utils/webSocketUtils");
const RobotConst = require("./common/wpkRobotConst");
const msgReceive = require("./wpkRobotMsgReceive");
const MsgSend = require("./wpkRobotMsgSend");
const RobotUtils = require("./common/wpkRobotUtils");
const RobotHttpReq = require("./wpkRobotHttpReq");

const U = Utils.U,ProtoUtil = RobotUtils.ProtoUtil;
const clientList = [], conInfo = {};
class RobotWSConnect{
    // 消息回调方法设置
    static setMsgCallbackMap(cMap, cId, callback) {
        let now = U.now();
        if (callback) {
            cMap.laseClearTime = cMap.laseClearTime || now;
            cMap[cId] = {c: callback, t: now};
        }
        // 每半个小时清理,过期的
        if (now - cMap.laseClearTime > 1800000) {
            U.each(cMap, (k, v) => (now - v.t > 90000) && (delete cMap[mdReq.callbackId]));
        }
    }

    // 连接
    static wsConnect(urlPath, userId, roomId, key, callback) {
        let cli = new webSocketUtils.client();
        let user = cli.user = RobotHttpReq.getUser(userId);
        if (user == null) {
            return Utils.log(`${userId} 用户为空`);
        }
        let domain = RobotConst.cfg.wpkGameWsURL.split("//")[1].split("/")[0];
        cli.connect(`${RobotConst.cfg.wpkGameWsURL}${urlPath}/battle/echo?userId=${userId}&sessionToken=${user.sessionToken}&connectTime=${U.now()}&roomId=${roomId}&key=${encodeURIComponent(key)}&u=${encodeURIComponent(btoa(domain))}`);
        cli.userId = user.userId;
        cli.roomId = roomId;
        let mdReq = {targetId: roomId, stubInfo: {userId: user.userId, version: "5.7.12"}};
        let callbackMap = {}; // 消息回调
        cli.onopen = () => {
            callback(cli);
            if (U.find(clientList, c => c.userId == cli.userId) == null) {
                clientList.push(cli);
            }
            cli.pongTime = U.now()
        }
        cli.onmessage = data => {
            Utils.tryCatch(_ => {
                let resp;
                try {
                    resp = ProtoUtil.decode("MsgDeliverResp", encryptUtils.decryptAESBytes(data, user.gameAesKey))
                } catch (e) {
                    resp = ProtoUtil.decode("MsgDeliverResp", new Uint8Array(data)); // 解密失败,使用原始数据解析
                }
                let callback = callbackMap[resp.callbackId];
                resp.msgBody = resp.msgBody ? ProtoUtil.decode(RobotConst.msgTypeMap[resp.msgType], resp.msgBody) : null;
                callback && callback.c(resp, resp.msgType, resp.msgBody);
                msgReceive.msgReceive(cli, resp.msgType, resp.msgBody, resp)
            }, e => cli.log("wpkOnmessage", e));
        }
        cli.send = (msgType, data, callback) => {
            msgType != "PongMessage" && cli.log(`WS -> ${msgType}:`, data);
            mdReq.stubInfo.sessionToken = user.sessionToken;
            mdReq.callbackId = Utils.guid().substring(0, 4);
            RobotWSConnect.setMsgCallbackMap(callbackMap, mdReq.callbackId, callback);
            data = data ? ProtoUtil.encode(RobotConst.msgTypeMap[msgType] || msgType, data) : null;
            Utils.tryCatch(_ => cli.ws.send(ProtoUtil.encode("MsgDeliverReq", U.extend({}, mdReq, {msgType: msgType, msgBody: data}))));
        }
        cli.log = (...param) => {
            if (!RobotConst.cfg.printLogUserId || RobotConst.cfg.printLogUserId == userId) {
                param.splice(U.first(param) == "error" ? 1 : 0, 0, userId);
                Utils.log(...param, "stackTrace_3");
            }
        }
        cli.onclose = () => {
            console.warn("onclose", roomId, userId);
        }
        cli.disconnect = () => {
            for (let i = 0; i < clientList.length; i++) {
                const c = clientList[i];
                if (c.userId === userId) {
                    clientList.splice(i, 1);
                    Utils.tryCatch(_ => cli.ws.close());
                    console.warn("disconnect", roomId, userId);
                    return;
                }
            }
        }
        cli.M = new MsgSend(cli);
        cli.receiveLog = RobotConst.robotUserList.length < 10; // 10人一下才输出接收消息日志
        cli.isOpen = () => cli.ws.readyState == 1;  // 是否连接成功
        return cli;
    }

    // 断开某个房间的连接
    static wsDisconnect(roomId) {
        for (let i = 0; i < clientList.length; i++) {
            const cli = clientList[i];
            if (cli.roomId == roomId) {
                clientList.splice(i, 1);
                cli.ws.send("C_exitRoom", null, () => cli.ws.close());
                return RobotWSConnect.wsDisconnect(roomId);
            }
        }
    }

    static wsExit(userId, roomId) {
        for (let i = 0; i < clientList.length; i++) {
            const cli = clientList[i];
            if ((roomId == -1 || cli.roomId == roomId) && cli.userId == userId) {
                clientList.splice(i, 1);
                cli.M.exitRoom(() => cli.ws.close());
                return;
            }
        }
    }

    static state() {
        // aggregate clientList by roomId
        let rooms = clientList.reduce((acc, c) => {
            acc[c.roomId] = acc[c.roomId] || [];
            acc[c.roomId].push(c.userId);
            return acc;
        }, {});
        return Object.keys(rooms).map(roomId => ({roomId: roomId, userIds: rooms[roomId]}));
    }

    // 获得Client
    static getClient(userId) {
        return U.find(clientList, c => c.userId == userId);
    }

    // 获得连接信息
    static getConnectInfo() {
        return conInfo;
    }
}

// 重连处理
function wsReConnect() {
    setInterval(() => {
        let now = U.now(), timeoutNum = 0, cliLen = U.size(clientList);
        U.each(clientList,cli=>{
            cli.pongTime = cli.pongTime || now;
            // 发送心跳(间隔7秒)
            cli.send("PongMessage", null, _ => cli.pongTime = U.now());
            // 超时尝试重连
            if (now - cli.pongTime > 16000) {
                console.warn("timeout", cli.roomId, cli.userId);
                setTimeout(() => {
                    Utils.tryCatch(_ => cli.ws.close());
                    Utils.tryCatch(_ => cli.reconnect());
                }, U.random(5, 1000));
                timeoutNum++;
            }
        })
        // 设置连接信息
        U.extend(conInfo, {curNum: cliLen - timeoutNum, sumNum: cliLen, timeoutNum: timeoutNum});
    }, 7000);
}

wsReConnect();

module.exports = RobotWSConnect;