const http = require("http");
const Utils = require("../commoms/utils/nodeCommonUtils");
const RobotConst = require("./common/wpkRobotConst");
const connect = require("./wpkRobotWSConnect");
const RobotHttpReq = require("./wpkRobotHttpReq");

const U = Utils.U;
let robotMainService = null;

class HttpServer {
    constructor() {
        this.rMap = {};
    }

    // 启动http服务
    startHttpServer(RobotMain) {
        robotMainService = RobotMain;
        let self = this, port = RobotConst.cfg.serverPort || RobotConst.HTTP_SERVER_PORT;
        http.createServer(function (req, res) {
            let url = req.url, uArr = url.split("?"), uri = uArr[0], param = uArr[1] || "", mapVal = self.rMap[uri];
            self.mapApi(uri, param, mapVal, req, res);
        }).listen(port);
        Utils.log(`http server start success! http://localhost:${port}/getIndexInfo`)
    }

    mapApi(uri, param, mapVal, req, res) {
        let paramMap = {};
        (param + "&").replace(/(.*?)=(.*?)&/g, (p, p1, p2) => (paramMap[p1] = p2));
        res.writeHead(200, {"Content-type": "application/json; charset=utf-8"});
        if (!mapVal) {
            res.end("404");
            return;
        }
        try {
            const result = mapVal(paramMap);
            Promise.resolve(result)
                .then(data => res.end(JSON.stringify(this.respSuc(data))))
                .catch(err => {
                    Utils.log(`Error in route ${uri}:`, err);
                    res.end(JSON.stringify(this.respFail(1, err && err.message ? err.message : "Internal error")));
                });
        } catch (err) {
            Utils.log(`Error in route ${uri}:`, err);
            res.end(JSON.stringify(this.respFail(1, err && err.message ? err.message : "Internal error")));
        }
    }

    // 路由映射
    router(uri, func) {
        this.rMap[uri] = func
    }

    respSuc(data) {
        return {code: 0, data: data}
    }

    respFail(code, msg) {
        return {code, msg};
    }
}

let web = new HttpServer()

// 首页信息
web.router("/getIndexInfo", () => {
    return {
        conInfo: connect.getConnectInfo(),
        robotGroup: RobotConst.userGroup
    };
})

// 启动机器人
web.router("/startRobot", (param) => {
    RobotConst.roomCfg = JSON.parse(decodeURIComponent(param.roomCfg));
    robotMainService.startRobot(parseInt(param.robotNum));
})

// 停止机器人
web.router("/stopRobot", () => {
    robotMainService.stopRobot();
})

web.router("/createRoom", (param) => {
    RobotHttpReq.createRoom(param.userId, (r, userId) => {});
})

web.router("/joinRoom", (param) => {
    robotMainService.joinRoom(param.roomId, param.userId, param.seatNum);
})

web.router("/exitRoom", (param) => {
    robotMainService.exitRoom(param.userId, param.roomId);
})

web.router("/state", () => {
    return robotMainService.state();
})

web.router("/listRooms", async (param) => {
    return await new Promise(resolve => {
        return RobotHttpReq.getRoomRecordByCreateView(param.userId, (r) => {
            resolve(r)
        });
    })
})

module.exports = web;