import { UnrecoverableError } from 'bullmq';
import { scanAndFetch } from './scanning';
import { JobDataHandler } from 'shared';

const { HttpApis } = require('./httpApis.js');
const { signupAndPlay } = require('./mtt.js');
const MttConfig = require('./config.js');

class MttMain {
    private onMessage: JobDataHandler;
    private storeTournamentDetails: Function;
    private fetchTournamentDetails: Function;

    static init(onMessage: JobDataHandler, storeTournamentDetails: Function, fetchTournamentDetails: Function) {
        mtt.onMessage = onMessage;
        mtt.storeTournamentDetails = storeTournamentDetails;
        mtt.fetchTournamentDetails = fetchTournamentDetails;
    }

    static async run(
        token: string,
        action: string,
        tournamentId: number,
        ticketId: number,
        nickname: string,
    ): Promise<void> {
        switch (action) {
            case 'scan':
                return mtt.scan(token);
            case 'play':
                return mtt.play(token, tournamentId, ticketId);
            case 'check':
                return mtt.check(token, tournamentId, nickname);
            default:
                throw new UnrecoverableError(`Unknown action: ${action}`);
        }
    }

    async scan(token: string): Promise<void> {
        return scanAndFetch(token, this.onMessage, this.storeTournamentDetails);
    }

    async play(token: string, tournamentId: number, ticketId: number) {
        return signupAndPlay(token, tournamentId, ticketId, this.onMessage, this.fetchTournamentDetails);
    }

    async check(token: string, tournamentId: number, nickname: string): Promise<void> {
        return new Promise((resolve, reject) => {
            const httpApis = new HttpApis();

            httpApis.requestMttTournamentPlayers(
                token,
                tournamentId,
                nickname,
                (data: any) => {
                    if (data.ErrorCode) {
                        reject(data.ErrorCode);
                    } else {
                        this.onMessage({ registered: data.PlayersDetail?.length > 0 });
                        resolve();
                    }
                },
                (error: any) => {
                    reject(error);
                },
            );
        });
    }
}

const mtt = new MttMain();

export { MttMain, MttConfig };
