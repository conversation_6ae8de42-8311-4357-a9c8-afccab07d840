# Insurance Decision Logic Correction Summary

## Issue Identified

The initial implementation incorrectly interpreted the `outsCount` parameter as representing **our outs** (cards that would improve our hand), when it actually represents **opponent's outs** (cards that would improve the opponent's hand to beat ours).

## Correction Applied

### Probability Logic Inversion

**Before (Incorrect):**
- Few outs (≤3): 85% probability to buy insurance
- Many outs (>7): 40% probability to buy insurance

**After (Corrected):**
- Few opponent outs (≤3): 10% probability to buy insurance
- Many opponent outs (>7): 55% probability to buy insurance

### Strategic Rationale

**Corrected Understanding:**
- **More opponent outs** = higher chance opponent improves = **higher probability we should buy insurance**
- **Fewer opponent outs** = lower chance opponent improves = **lower probability we should buy insurance**

### Updated Probability Thresholds

```typescript
if (outsCount <= 3) {
    buyProbability = 0.10; // Low probability - opponent has few ways to improve
} else if (outsCount <= 5) {
    buyProbability = 0.25; // Medium-low probability - moderate opponent threat
} else if (outsCount <= 7) {
    buyProbability = 0.35; // Medium probability - significant opponent threat
} else {
    buyProbability = 0.55; // High probability - opponent has many ways to improve
}
```

### Updated Coverage Amount Distributions

**Few opponent outs (≤3):** Lower risk, prefer smaller coverage amounts
```
12: 50%, 20: 35%, 33: 15%
```

**Medium opponent outs (≤5):** Moderate risk, balanced coverage
```
12: 30%, 20: 40%, 33: 20%, 50: 10%
```

**More opponent outs (≤7):** Higher risk, prefer medium to high coverage
```
20: 25%, 33: 35%, 50: 30%, 100: 10%
```

**Many opponent outs (>7):** High risk, prefer higher coverage amounts
```
33: 20%, 50: 40%, 100: 40%
```

## Files Updated

1. **`libs/pkw/src/pkw_ts/network/GameNetWork.ts`**
   - Updated `shouldBuyInsurance()` method with inverted probability logic
   - Updated `selectCoverageAmount()` method with adjusted coverage distributions
   - Updated documentation comments to clarify opponent's outs

2. **`tests/insurance-decision.test.ts`**
   - Updated test cases to reflect new coverage amount distributions
   - Updated test descriptions to clarify opponent's outs context

3. **`docs/insurance-decision-implementation.md`**
   - Updated all documentation to reflect corrected understanding
   - Updated strategic rationale section
   - Updated usage examples

## Validation

- ✅ All tests pass (3/3)
- ✅ Project builds successfully
- ✅ Logic now correctly reflects poker theory for insurance decisions
- ✅ Documentation updated to match implementation

## Key Insight

Insurance in poker is protection against the opponent improving their hand. Therefore:
- When opponent has many outs (many ways to improve), insurance becomes more valuable
- When opponent has few outs (few ways to improve), insurance becomes less valuable

This correction ensures the bot makes strategically sound insurance decisions based on the actual risk posed by the opponent's potential improvements.
