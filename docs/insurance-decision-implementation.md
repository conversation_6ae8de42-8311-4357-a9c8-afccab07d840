# Insurance Decision Logic Implementation

## Overview

This document describes the implementation of the dynamic insurance decision logic in the `buildBuyInsuranceMsg` method within `libs/pkw/src/pkw_ts/network/GameNetWork.ts`.

## Implementation Details

### Core Functionality

The insurance decision system has been enhanced to make intelligent decisions based on the number of "outs" (cards that can improve the player's hand). The implementation replaces the previous hardcoded `amount: 200, is_buy: true` with dynamic logic.

### Decision Framework

#### 1. Insurance Purchase Decision (`shouldBuyInsurance`)

The system uses probability thresholds based on outs count:

- **≤3 outs**: 85% probability to buy insurance (strong hand, high protection value)
- **≤5 outs**: 70% probability to buy insurance (medium-strong hand)
- **≤7 outs**: 55% probability to buy insurance (medium hand)
- **>7 outs**: 40% probability to buy insurance (weaker hand, lower protection value)

#### 2. Coverage Amount Selection (`selectCoverageAmount`)

Uses weighted probability distributions for coverage amounts:

**Few outs (≤3)**: Conservative approach, prefer higher coverage
```
50: 80%, 12: 15%, 20: 5%
```

**Medium outs (≤5)**: Balanced approach
```
50: 30%, 12: 40%, 20: 20%, 33: 10%
```

**More outs (≤7)**: Varied coverage options
```
12: 20%, 20: 30%, 33: 30%, 50: 15%, 100: 5%
```

**Many outs (>7)**: Higher coverage amounts
```
20: 10%, 33: 30%, 50: 30%, 100: 30%
```

### Implementation Components

#### Main Method: `buildBuyInsuranceMsg`

```typescript
private buildBuyInsuranceMsg(
    outsCount: number,
    roomId: number,
    action_seq: number,
): IRequestBuyInsurance
```

**Parameters:**
- `outsCount`: Number of outs (cards that can improve the hand)
- `roomId`: Room identifier
- `action_seq`: Action sequence number from the game

**Returns:** `IRequestBuyInsurance` object with:
- `roomid`: Room identifier
- `action_seq`: Action sequence number
- `amount`: Selected coverage amount (0 if not buying)
- `is_buy`: Boolean indicating whether to buy insurance

#### Helper Methods

1. **`shouldBuyInsurance(outsCount: number): boolean`**
   - Determines whether to buy insurance based on probability thresholds
   - Uses `Math.random()` for decision making

2. **`selectCoverageAmount(outsCount: number): number`**
   - Selects coverage amount using weighted probability distributions
   - Calls `weightedRandomSelection` for amount selection

3. **`weightedRandomSelection(weights: {[value: number]: number}): number`**
   - Performs weighted random selection from coverage options
   - Uses cumulative probability distribution for selection

### Strategic Rationale

#### Outs-Based Decision Making

The logic is based on poker theory where:
- **Fewer outs** = stronger current hand = higher insurance value
- **More outs** = weaker current hand = lower insurance value

#### Coverage Amount Strategy

- **Strong hands (few outs)**: Prefer higher coverage to protect significant equity
- **Weak hands (many outs)**: Use varied coverage amounts, often lower values
- **Medium hands**: Balanced approach with multiple coverage options

### Logging and Monitoring

The implementation includes comprehensive logging:

```typescript
logging.withTag('INSURANCE_DECISION').info('Insurance decision made', {
    outsCount,
    shouldBuy: shouldBuyInsurance,
    amount: coverageAmount,
    roomId
});
```

This enables monitoring and analysis of insurance decisions for optimization.

### Testing

A comprehensive test suite (`tests/insurance-decision.test.ts`) validates:

1. **Coverage Amount Validation**: Ensures only expected amounts are returned for each outs range
2. **Message Structure**: Verifies consistent return object structure
3. **Edge Cases**: Tests with 0 outs and very high outs counts
4. **Probability Distribution**: Validates that all possible amounts can be selected

### Integration

The implementation seamlessly integrates with the existing game network infrastructure:

- Maintains the same `IRequestBuyInsurance` interface
- Preserves existing method signature
- Uses existing logging infrastructure
- Compatible with current game flow

### Performance Considerations

- **Minimal overhead**: Simple probability calculations
- **No external dependencies**: Uses only built-in JavaScript functions
- **Deterministic fallback**: Guaranteed to return valid coverage amounts
- **Memory efficient**: No persistent state or caching required

## Usage Example

```typescript
// Called automatically when insurance opportunity arises
const insuranceMsg = this.buildBuyInsuranceMsg(
    msg.outs.length,  // Number of outs from game state
    msg.roomid,       // Room ID from notification
    msg.action_seq    // Action sequence from notification
);

// Result examples:
// Few outs (2): { roomid: 12345, action_seq: 100, amount: 50, is_buy: true }
// Many outs (10): { roomid: 12345, action_seq: 100, amount: 0, is_buy: false }
```

## Future Enhancements

Potential improvements could include:

1. **Player-specific profiles**: Adjust probabilities based on player history
2. **Pot size consideration**: Factor in pot size for coverage decisions
3. **Opponent modeling**: Consider opponent tendencies
4. **Dynamic probability adjustment**: Learn from results over time
5. **Risk tolerance settings**: Configurable risk parameters

## Conclusion

This implementation provides a sophisticated, poker-theory-based approach to insurance decisions that adapts to hand strength while maintaining the flexibility to handle various game scenarios. The modular design allows for easy testing, monitoring, and future enhancements.
