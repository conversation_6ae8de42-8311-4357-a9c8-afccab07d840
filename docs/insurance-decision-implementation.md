# Insurance Decision Logic Implementation

## Overview

This document describes the implementation of the dynamic insurance decision logic in the `buildBuyInsuranceMsg` method within `libs/pkw/src/pkw_ts/network/GameNetWork.ts`.

## Implementation Details

### Core Functionality

The insurance decision system has been enhanced to make intelligent decisions based on the number of opponent's "outs" (cards that would improve the opponent's hand to beat ours). The implementation replaces the previous hardcoded `amount: 200, is_buy: true` with dynamic logic.

### Decision Framework

#### 1. Insurance Purchase Decision (`shouldBuyInsurance`)

The system uses probability thresholds based on opponent's outs count:

- **≤3 opponent outs**: 10% probability to buy insurance (opponent has few ways to improve)
- **≤5 opponent outs**: 25% probability to buy insurance (moderate opponent threat)
- **≤7 opponent outs**: 35% probability to buy insurance (significant opponent threat)
- **>7 opponent outs**: 55% probability to buy insurance (opponent has many ways to improve)

#### 2. Coverage Amount Selection (`selectCoverageAmount`)

Uses weighted probability distributions for coverage amounts based on opponent's outs:

**Few opponent outs (≤3)**: Lower risk, prefer smaller coverage amounts

```
12: 50%, 20: 35%, 33: 15%
```

**Medium opponent outs (≤5)**: Moderate risk, balanced coverage

```
12: 30%, 20: 40%, 33: 20%, 50: 10%
```

**More opponent outs (≤7)**: Higher risk, prefer medium to high coverage

```
20: 25%, 33: 35%, 50: 30%, 100: 10%
```

**Many opponent outs (>7)**: High risk, prefer higher coverage amounts

```
33: 20%, 50: 40%, 100: 40%
```

### Implementation Components

#### Main Method: `buildBuyInsuranceMsg`

```typescript
private buildBuyInsuranceMsg(
    outsCount: number,
    roomId: number,
    action_seq: number,
): IRequestBuyInsurance
```

**Parameters:**

- `outsCount`: Number of opponent's outs (cards that would improve opponent's hand to beat ours)
- `roomId`: Room identifier
- `action_seq`: Action sequence number from the game

**Returns:** `IRequestBuyInsurance` object with:

- `roomid`: Room identifier
- `action_seq`: Action sequence number
- `amount`: Selected coverage amount (0 if not buying)
- `is_buy`: Boolean indicating whether to buy insurance

#### Helper Methods

1. **`shouldBuyInsurance(outsCount: number): boolean`**
    - Determines whether to buy insurance based on probability thresholds
    - Uses `Math.random()` for decision making

2. **`selectCoverageAmount(outsCount: number): number`**
    - Selects coverage amount using weighted probability distributions
    - Calls `weightedRandomSelection` for amount selection

3. **`weightedRandomSelection(weights: {[value: number]: number}): number`**
    - Performs weighted random selection from coverage options
    - Uses cumulative probability distribution for selection

### Strategic Rationale

#### Opponent Outs-Based Decision Making

The logic is based on poker theory where:

- **Fewer opponent outs** = opponent has fewer ways to improve = lower insurance value
- **More opponent outs** = opponent has more ways to improve = higher insurance value

#### Coverage Amount Strategy

- **Few opponent outs**: Lower risk scenario, prefer smaller coverage amounts to minimize cost
- **Many opponent outs**: High risk scenario, prefer higher coverage amounts for better protection
- **Medium opponent outs**: Balanced approach with varied coverage options

### Logging and Monitoring

The implementation includes comprehensive logging:

```typescript
logging.withTag('INSURANCE_DECISION').info('Insurance decision made', {
    outsCount,
    shouldBuy: shouldBuyInsurance,
    amount: coverageAmount,
    roomId,
});
```

This enables monitoring and analysis of insurance decisions for optimization.

### Testing

A comprehensive test suite (`tests/insurance-decision.test.ts`) validates:

1. **Coverage Amount Validation**: Ensures only expected amounts are returned for each outs range
2. **Message Structure**: Verifies consistent return object structure
3. **Edge Cases**: Tests with 0 outs and very high outs counts
4. **Probability Distribution**: Validates that all possible amounts can be selected

### Integration

The implementation seamlessly integrates with the existing game network infrastructure:

- Maintains the same `IRequestBuyInsurance` interface
- Preserves existing method signature
- Uses existing logging infrastructure
- Compatible with current game flow

### Performance Considerations

- **Minimal overhead**: Simple probability calculations
- **No external dependencies**: Uses only built-in JavaScript functions
- **Deterministic fallback**: Guaranteed to return valid coverage amounts
- **Memory efficient**: No persistent state or caching required

## Usage Example

```typescript
// Called automatically when insurance opportunity arises
const insuranceMsg = this.buildBuyInsuranceMsg(
    msg.outs.length, // Number of opponent's outs from game state
    msg.roomid, // Room ID from notification
    msg.action_seq, // Action sequence from notification
);

// Result examples:
// Few opponent outs (2): { roomid: 12345, action_seq: 100, amount: 20, is_buy: false } (low probability)
// Many opponent outs (10): { roomid: 12345, action_seq: 100, amount: 100, is_buy: true } (high probability)
```

## Future Enhancements

Potential improvements could include:

1. **Player-specific profiles**: Adjust probabilities based on player history
2. **Pot size consideration**: Factor in pot size for coverage decisions
3. **Opponent modeling**: Consider opponent tendencies
4. **Dynamic probability adjustment**: Learn from results over time
5. **Risk tolerance settings**: Configurable risk parameters

## Conclusion

This implementation provides a sophisticated, poker-theory-based approach to insurance decisions that adapts to hand strength while maintaining the flexibility to handle various game scenarios. The modular design allows for easy testing, monitoring, and future enhancements.
