import { describe, it } from 'node:test';
import assert from 'node:assert';

// Mock the cv object and other dependencies
const mockCv = {
    GameDataManager: {
        tRoomData: {
            u32RoomId: 12345
        }
    },
    dataHandler: {
        getUserData: () => ({
            u32Uid: 67890
        })
    }
};

// Mock the logging module
const mockLogging = {
    withTag: () => ({
        info: () => {}
    })
};

// Mock the protocol module
const mockProtocol = {
    IRequestBuyInsurance: {}
};

// Create a test class that extends the insurance logic
class TestInsuranceLogic {
    /**
     * Determines whether to buy insurance based on the number of outs
     */
    private shouldBuyInsurance(outsCount: number): boolean {
        let buyProbability: number;
        
        if (outsCount <= 3) {
            buyProbability = 0.85;
        } else if (outsCount <= 5) {
            buyProbability = 0.70;
        } else if (outsCount <= 7) {
            buyProbability = 0.55;
        } else {
            buyProbability = 0.40;
        }
        
        const randomValue = Math.random();
        return randomValue < buyProbability;
    }

    /**
     * Selects coverage amount using weighted probability distributions
     */
    private selectCoverageAmount(outsCount: number): number {
        let coverageWeights: { [amount: number]: number };
        
        if (outsCount <= 3) {
            coverageWeights = { 50: 80, 12: 15, 20: 5 };
        } else if (outsCount <= 5) {
            coverageWeights = { 50: 30, 12: 40, 20: 20, 33: 10 };
        } else if (outsCount <= 7) {
            coverageWeights = { 12: 20, 20: 30, 33: 30, 50: 15, 100: 5 };
        } else {
            coverageWeights = { 20: 10, 33: 30, 50: 30, 100: 30 };
        }
        
        return this.weightedRandomSelection(coverageWeights);
    }

    /**
     * Performs weighted random selection
     */
    private weightedRandomSelection(weights: { [value: number]: number }): number {
        const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
        const randomValue = Math.random() * totalWeight;
        
        let cumulativeWeight = 0;
        for (const [value, weight] of Object.entries(weights)) {
            cumulativeWeight += weight;
            if (randomValue <= cumulativeWeight) {
                return parseInt(value, 10);
            }
        }
        
        return parseInt(Object.keys(weights)[0], 10);
    }

    /**
     * Public method to test the insurance decision logic
     */
    public buildBuyInsuranceMsg(outsCount: number, roomId: number, action_seq: number) {
        const shouldBuyInsurance = this.shouldBuyInsurance(outsCount);
        
        if (!shouldBuyInsurance) {
            return {
                roomid: roomId,
                action_seq: action_seq,
                amount: 0,
                is_buy: false,
            };
        }

        const coverageAmount = this.selectCoverageAmount(outsCount);
        
        return {
            roomid: roomId,
            action_seq: action_seq,
            amount: coverageAmount,
            is_buy: true,
        };
    }
}

describe('Insurance Decision Logic', () => {
    const testLogic = new TestInsuranceLogic();

    it('should return valid coverage amounts for different outs counts', () => {
        const testCases = [
            { outsCount: 2, expectedAmounts: [50, 12, 20] },
            { outsCount: 4, expectedAmounts: [50, 12, 20, 33] },
            { outsCount: 6, expectedAmounts: [12, 20, 33, 50, 100] },
            { outsCount: 10, expectedAmounts: [20, 33, 50, 100] }
        ];

        testCases.forEach(({ outsCount, expectedAmounts }) => {
            // Test multiple times to check all possible amounts can be selected
            const results = new Set<number>();
            for (let i = 0; i < 1000; i++) {
                const result = testLogic.buildBuyInsuranceMsg(outsCount, 12345, 1);
                if (result.is_buy) {
                    results.add(result.amount);
                }
            }

            // Check that only expected amounts are returned
            for (const amount of results) {
                assert(expectedAmounts.includes(amount), 
                    `Unexpected amount ${amount} for outsCount ${outsCount}`);
            }
        });
    });

    it('should return consistent structure for insurance messages', () => {
        const result = testLogic.buildBuyInsuranceMsg(3, 12345, 100);
        
        assert(typeof result.roomid === 'number');
        assert(typeof result.action_seq === 'number');
        assert(typeof result.amount === 'number');
        assert(typeof result.is_buy === 'boolean');
        
        assert.strictEqual(result.roomid, 12345);
        assert.strictEqual(result.action_seq, 100);
    });

    it('should handle edge cases correctly', () => {
        // Test with 0 outs
        const result0 = testLogic.buildBuyInsuranceMsg(0, 12345, 1);
        assert(typeof result0.is_buy === 'boolean');
        
        // Test with very high outs
        const result20 = testLogic.buildBuyInsuranceMsg(20, 12345, 1);
        assert(typeof result20.is_buy === 'boolean');
        if (result20.is_buy) {
            assert([20, 33, 50, 100].includes(result20.amount));
        }
    });
});
