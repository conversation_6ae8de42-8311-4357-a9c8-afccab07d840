/* eslint-disable @typescript-eslint/no-unused-vars */
import process from 'node:process';
import { readFileSync, existsSync } from 'node:fs';
import dotenv from 'dotenv';


dotenv.config();
if (process.env.NODE_ENV) {
    dotenv.config({ path: `.env.${process.env.NODE_ENV}`, override: true });
}

if (existsSync('private.env')) {
    dotenv.config({ path: `private.env`, override: true });
}

import { fetchStrategy } from 'shared';

function pause(ms: number) {
    return new Promise((res) => setTimeout(res, ms));
}

const payload1 = JSON.parse(readFileSync('./tests/integration/payload1.json').toString());
const payload2 = JSON.parse(readFileSync('./tests/integration/payload2.json').toString());

const { mtt: mtt1, state: state1 } = payload1;
const { mtt: mtt2, state: state2 } = payload2;

const SESSIONS = parseInt(process.env.SESSIONS ?? '0') || 50;

const result_data: Record<string,any> = {};
const intervals: NodeJS.Timeout[] = [];

let counter = 0;
function createClientSession() {
    counter += 1;
    const sessionId = counter;
    const intervalTime = 4000 + Math.random() * 2 * 1000;
    console.log(JSON.stringify({msg: `Starting session ${sessionId}`}));

    let roundRobin = sessionId % 2;
    const intervalId = setInterval(async () => {
        const start = Date.now();
        let state, mtt;
        if (roundRobin === 1) {
            state = state1;
            mtt = mtt1;
            roundRobin = 0;
        } else if (roundRobin === 0) {
            state = state2;
            mtt = mtt2;
            roundRobin = 1;
        }

        let error = 0;
        let result;
        try {
            await fetchStrategy(state, mtt);

            result = Date.now() - start;
        } catch (err:any) {
            error = 1;
            console.log(JSON.stringify({session: sessionId, err}));

            if (error) {
                if (!result_data.error) result_data.error = 0;
                result_data.error += 1;
            }
            return;
        }

        if (result <= 1000) {
            if (!result_data.below1000) result_data.below1000 = 0;
            result_data.below1000 += 1;
        } else if (result <= 1200) {
            if (!result_data.below1200) result_data.below1200 = 0;
            result_data.below1200 += 1;
        } else if (result <= 2000) {
            if (!result_data.below2000) result_data.below2000 = 0;
            result_data.below2000 += 1;
        }  else {
            if (!result_data.others) result_data.others = 0;
            result_data.others +=1;
        }
    }, intervalTime);



    intervals.push(intervalId);
}

(async function glueLoadImitation() {
    const start = Date.now();
    for (let i = 0; i < SESSIONS; i++) {
        // await pause(50);
        createClientSession();
    }

    setInterval(() => {
        const passed = Math.floor(Date.now() - start)/1000;
        console.log(JSON.stringify({Passed: `${passed} sec`, result_data}));
    }, 10000);
})();
