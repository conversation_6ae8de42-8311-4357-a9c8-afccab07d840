import { GamePlatformAdapter, TransferJobData } from './models/model';
import { JobDataHandler, JobType, JobData, logging, LoginError, UserData } from 'shared';
import { PlatformStrategy } from './platforms/platform-strategy';

export class PlatformAwareGameEntrypoint {
    private platformStrategy: PlatformStrategy;
    private gameAdapter: GamePlatformAdapter;
    private onMessage: JobDataHandler = () => {};

    constructor(platformStrategy: PlatformStrategy, gameAdapter: GamePlatformAdapter) {
        this.platformStrategy = platformStrategy;
        this.gameAdapter = gameAdapter;
    }

    async init(proxyUrl?: string): Promise<void> {
        this.platformStrategy.init(proxyUrl);
        await this.gameAdapter.init(proxyUrl);
    }

    async start(user: UserData, action: JobType, tableId: number, params: JobData, onMessage: JobDataHandler): Promise<void> {
        const platformUser = await this.platformStrategy.login(user);
        try {
            switch (action) {
                case JobType.CHECK:
                case JobType.SCAN:
                case JobType.PLAY: {
                    let userData: any = platformUser;
                    if (this.gameAdapter.isMtt()) {
                        userData = await this.platformStrategy.fetchMttUserData(platformUser);
                    }
                    return await this.gameAdapter.start(userData, action, tableId, params, onMessage);
                }
                case JobType.TRANSFER:
                    return await this.platformStrategy.transfer(platformUser, params as TransferJobData);
                case JobType.BALANCE: {
                    const balance = await this.platformStrategy.balance(platformUser);
                    return this.onMessage({ balance });
                }
                default:
                    return Promise.reject(new Error(`Unsupported action: ${action}`));
            }
        } catch (e) {
            logging.error(`Error during action '${action}' for userId ${user.userId}:`, e);
            if (e instanceof LoginError) {
                logging.info(`Login error detected, clearing login cache for userId: ${user.userId}`);
                await this.platformStrategy.clearCache(user.userId);
            }
            throw e;
        }
    }

    async stop(action: JobType): Promise<void> {
        await this.gameAdapter.stop(action);
    }
}
