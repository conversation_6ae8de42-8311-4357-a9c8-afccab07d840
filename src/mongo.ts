import mongoose from 'mongoose';
import { randomUUID } from 'crypto';

import { AddUserAccountsResponse } from './controller';
import { AppType, PlatformType } from 'shared';
import { UserAccountRequestBody } from './models/api';
import { encryptUserInformation, decryptUserInformation } from './encryption';

export async function initMongoDBConnection() {
    try {
        const mongoHost = process.env.MONGO_HOST || 'localhost';
        const mongoUser = process.env.MONGO_USER || 'test';
        const mongoPwd = process.env.MONGO_PASSWORD || 'test';
        const mongoURI = `mongodb://${mongoUser}:${mongoPwd}@${mongoHost}:27017/worker?directConnection=true&tls=true&retryWrites=false`;
        await mongoose.connect(mongoURI);
    } catch (error) {
        throw new Error(`MongoDB connection failed: ${error}`);
    }
}

// define schema
export const userAccountSchema = new mongoose.Schema({
    playerId: String,
    userInformation: String,
    appId: Number,
    platformId: Number,
});

// compile schema to model
const UserAccount = mongoose.model('UserAccount', userAccountSchema, 'user_accounts');

export async function addUserAccounts(
    requestBody: UserAccountRequestBody,
): Promise<AddUserAccountsResponse[]> {
    const userAccountsModels: mongoose.Document[] = [];
    const addUserAccountsResponses: AddUserAccountsResponse[] = [];

    const appId = requestBody.appId as AppType;

    for (const userAccountRequest of requestBody.userAccounts) {
        const { playerId, ...userInfo } = userAccountRequest;
        const userInformationEncrypted = encryptUserInformation(JSON.stringify(userInfo));
        const platformId = userAccountRequest.platformId as PlatformType;
        const currPlayerId = playerId ? playerId : randomUUID();
        const userAccount = new UserAccount({
            playerId: currPlayerId,
            userInformation: userInformationEncrypted,
            appId,
            platformId,
        });
        userAccountsModels.push(userAccount);
        const userAccountResponse: AddUserAccountsResponse = {
            playerId: currPlayerId,
        };
        addUserAccountsResponses.push(userAccountResponse);
    }
    await UserAccount.insertMany(userAccountsModels);
    return addUserAccountsResponses;
}

export async function dropUserAccounts(playerIds: string[]) {
    await UserAccount.deleteMany({ playerId: { $in: playerIds } }).exec();
}

export async function getUserAccount(playerId: string | undefined) {
    const userAccount = await mongoose.model('UserAccount').findOne({ playerId: playerId });
    if (!userAccount) {
        return null;
    }
    return decryptUserInformation(userAccount.userInformation!);
}
