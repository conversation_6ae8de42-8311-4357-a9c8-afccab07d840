import { Job<PERSON>ata<PERSON><PERSON><PERSON>, <PERSON>T<PERSON>, JobData, LoginError, MttUserData, MttConfigType } from 'shared';
import { MttMain } from 'mtt';
import { MTT_CONFIG_MTTAPI, MTT_CONFIG_MTTGAME, MTT_CONFIG_MTTWORLD } from '../config';
import { GamePlatformAdapter } from '../models/model';
import redis from '../redis';

class MttAdapterClass implements GamePlatformAdapter {
    async init(onMessage: JobDataHandler, proxyUrl?: string): Promise<void> {
        // Setting up MTT config
        const envMttConfig: MttConfigType = {
            mttWorld: MTT_CONFIG_MTTWORLD,
            mttGame: MTT_CONFIG_MTTGAME,
            mttApi: MTT_CONFIG_MTTAPI,
        };

        MttMain.init(
            onMessage,
            saveDataToCache,
            fetchDataFromCache,
            envMttConfig,
            proxyUrl,
        );
    }

    async start(
        userData: MttUserData,
        action: JobType,
        tournamentId: number,
        jobData: JobData,
    ): Promise<void> {
        if (!userData.mtt?.token) {
            throw new LoginError('mtt token not found');
        }

        const { profileName } = jobData;

        const ticketId = jobData.ticketId ? Number(jobData.ticketId) : 0;
        return MttMain.run(
            userData.mtt.token,
            action,
            tournamentId,
            ticketId,
            userData?.user?.nickname,
            profileName,
        );
    }

    async stop(_: JobType) {}

    isMtt(): boolean {
        return true;
    }
}

async function saveDataToCache(id: number, data: any) {
    redis.store(`mtt:${id}`, JSON.stringify(data));
}

async function fetchDataFromCache(id: number) {
    const result = await redis.fetch(`mtt:${id}`);
    if (result) {
        return JSON.parse(result);
    }
    return null;
}

export const MttAdapter = new MttAdapterClass();
