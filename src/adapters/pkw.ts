import { UnrecoverableError } from 'bullmq';
import {
    GameMode,
    GameType,
    JobDataHandler,
    JobType,
    JobData,
    logging,
    RoomStartParams,
    PkwUserData,
} from 'shared';
import { CurrencyType } from '../types';
import { PkwMain } from 'pkw';
import { GamePlatformAdapter } from '../models/model';

class PkwAdapterClass implements GamePlatformAdapter {
    constructor(
        private readonly currencies: CurrencyType[],
        private readonly gameId: number = 2,
        private readonly gameMode: GameMode = GameMode.NORMAL,
        private readonly gameType: GameType = GameType.NLHE,
    ) {}

    async init(onMessage: JobDataHandler, proxyUrl?: string, profileName?: string): Promise<void> {
        await PkwMain.init(
            this.currencies,
            onMessage,
            proxyUrl,
            this.gameId,
            this.gameMode,
            this.gameType,
            profileName,
        );
    }

    async start(userData: PkwUserData, jobType: JobType, tableId: number, params: JobData): Promise<void> {
        if (jobType === JobType.CHECK) {
            throw new UnrecoverableError('Unsupported game platform action: check');
        }
        const botParams: RoomStartParams = {
            buyInMultiplier: params.buyInMultiplier ?? 100,
            rebuyEnabled: params.rebuyEnabled ?? true,
            rebuyThreshold: params.rebuyThreshold ?? 50,
            withdrawAmount: params.withdrawAmount ?? 0,
            withdrawThreshold: params.withdrawThreshold ?? 0,
        };
        return new Promise((_, reject) => {
            PkwMain.onWPKLogin(userData, jobType, tableId, botParams, reject);
        });
    }

    async stop(jobType: JobType): Promise<void> {
        logging.info('[PKW Adapter] stop', jobType);
        if (jobType == JobType.PLAY) {
            await PkwMain.finishGame();
        }
    }

    isMtt(): boolean {
        return false;
    }
}

export const CashAdapter = new PkwAdapterClass([CurrencyType.USD, CurrencyType.GOLD]);
export const DiamondAdapter = new PkwAdapterClass([CurrencyType.DIAMOND]);
export const SplashAdapter = new PkwAdapterClass([CurrencyType.USD, CurrencyType.GOLD], 60, GameMode.SPLASH);
export const SplashDiamondAdapter = new PkwAdapterClass([CurrencyType.DIAMOND], 60, GameMode.SPLASH);
export const ZoomAdapter = new PkwAdapterClass([CurrencyType.USD, CurrencyType.GOLD], 40, GameMode.ZOOM);
export const ShortDeckAdapter = new PkwAdapterClass(
    [CurrencyType.USD, CurrencyType.GOLD],
    undefined,
    GameMode.NORMAL,
    GameType.SHORTDECK,
);
