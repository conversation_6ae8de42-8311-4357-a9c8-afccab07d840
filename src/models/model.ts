import { JobType, JobData } from 'shared';
import { CurrencyType } from '../types';

export interface TransferJobData extends JobData {
    currency: CurrencyType;
    transferAmount: number; // amount to transfer to receiver
    receiverId: number; // userId of the receiver for transfer
    receiverUsername?: string; // username of the receiver for transfer
}

export interface GamePlatformAdapter {
    init(onMessage: Function, proxyUrl?: string, profileName?: string): Promise<void>;
    start(user: any, action: JobType, tableId: number, params: JobData): Promise<void>;
    stop(action: JobType): Promise<void>;
    isMtt(): boolean;
}
