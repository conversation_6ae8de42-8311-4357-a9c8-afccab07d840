package projects

import a5labs.pipelineCommon.buildTypes.NamedServiceDockerBuildConfiguration
import a5labs.pipelineCommon.core.configuration.InfrastructureEnvironment
import a5labs.pipelineCommon.core.configuration.Jurisdiction
import a5labs.pipelineCommon.core.configuration.TerraformOutputParams
import a5labs.pipelineCommon.core.steps.variableRegistry
import a5labs.pipelineCommon.projectBuilders.GeneralDockerReleaseCiProject
import configuration.ProjectConfiguration

object AceGuardianStgReleaseCi : GeneralDockerReleaseCiProject({
    vcsUrl = ProjectConfiguration.ciVcsRoot
    imagesToBuild = listOf(
        NamedServiceDockerBuildConfiguration(name = "")
    )
    registryConfiguration = variableRegistry {
        variableName = TerraformOutputParams.DATA_WORKER_SERVICE_IMAGE_NAME
        environment = InfrastructureEnvironment.Staging
        jurisdiction = Jurisdiction.Wptg
    }
    addLatestTag = true
    restartEcs = true
    kashxaEnabled = false
    useCustomBaseImages = true
    ecsClusterARN = "arn:aws:ecs:eu-west-1:062605235943:cluster/aceguardian-security-bot-ecs"
    ecsServices = "'stg-aceguard-worker-svc'"
})