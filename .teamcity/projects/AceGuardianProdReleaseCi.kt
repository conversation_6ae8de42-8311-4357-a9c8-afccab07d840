package projects

import a5labs.pipelineCommon.buildTypes.NamedServiceDockerBuildConfiguration
import a5labs.pipelineCommon.core.configuration.InfrastructureEnvironment
import a5labs.pipelineCommon.core.configuration.Jurisdiction
import a5labs.pipelineCommon.core.configuration.TerraformOutputParams
import a5labs.pipelineCommon.core.steps.variableRegistry
import a5labs.pipelineCommon.projectBuilders.GeneralDockerReleaseCiProject
import configuration.ProjectConfiguration

object AceGuardianProdReleaseCi : GeneralDockerReleaseCiProject({
    vcsUrl = ProjectConfiguration.ciVcsRoot
    imagesToBuild = listOf(
        NamedServiceDockerBuildConfiguration(name = "")
    )
    registryConfiguration = variableRegistry {
        variableName = TerraformOutputParams.DATA_WORKER_SERVICE_IMAGE_NAME
        environment = InfrastructureEnvironment.Kashxa
        jurisdiction = Jurisdiction.Wptg
    }
    addLatestTag = true
    restartEcs = true
    kashxaEnabled = true
    useCustomBaseImages = true
    ecsClusterARN = "arn:aws:ecs:eu-west-1:001607371330:cluster/aceguardian-security-bot-ecs"
    ecsServices = "'prod-aceguard-worker-svc'"
})