package projects

import a5labs.pipelineCommon.buildTypes.NamedServiceDockerBuildConfiguration
import a5labs.pipelineCommon.core.configuration.InfrastructureEnvironment
import a5labs.pipelineCommon.core.configuration.Jurisdiction
import a5labs.pipelineCommon.core.configuration.TerraformOutputParams
import a5labs.pipelineCommon.core.steps.variableRegistry
import a5labs.pipelineCommon.projectBuilders.GeneralDockerReleaseCiProject
import configuration.ProjectConfiguration

object AceGuardianDevReleaseCi : GeneralDockerReleaseCiProject({
    vcsUrl = ProjectConfiguration.ciVcsRoot
    imagesToBuild = listOf(
        NamedServiceDockerBuildConfiguration(name = "")
    )
    registryConfiguration = variableRegistry {
        variableName = TerraformOutputParams.DATA_WORKER_SERVICE_IMAGE_NAME
        environment = InfrastructureEnvironment.Development
        jurisdiction = Jurisdiction.Wpto
    }
    addLatestTag = true
    restartEcs = true
    kashxaEnabled = false
    useCustomBaseImages = true
    ecsClusterARN = "arn:aws:ecs:ap-southeast-1:874789602627:cluster/aceguardian-security-bot-ecs"
    ecsServices = "'dev-aceguard-worker-svc'"
})