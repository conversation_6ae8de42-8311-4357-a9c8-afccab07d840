<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <name>End To End Service Config DSL Script</name>

    <properties>
        <serialization.version>1.2.0</serialization.version>
    </properties>


    <groupId>WorkerServicePipelines</groupId>
    <artifactId>WorkerServicePipelines_dsl</artifactId>
    <version>1.0-SNAPSHOT</version>

    <parent>
        <groupId>org.jetbrains.teamcity</groupId>
        <artifactId>configs-dsl-kotlin-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <repositories>
        <repository>
            <id>dev.maven-releases.afr</id>
            <name>dev.maven-releases.afr</name>
            <url>https://nexus.a5-labs-cloud.com/repository/dev.maven-releases.afr</url>
        </repository>
        <repository>
            <id>dev.maven-snapshots.afr</id>
            <name>dev.maven-snapshots.afr</name>
            <url>https://nexus.a5-labs-cloud.com/repository/dev.maven-snapshots.afr</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>jetbrains-all</id>
            <url>https://nexus.a5-labs-cloud.com/repository/prod.teamcity-central.afr</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>teamcity-server</id>
            <url>https://nexus.a5-labs-cloud.com/repository/prod.teamcity-maven.afr</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>jetbrains-all</id>
            <url>https://nexus.a5-labs-cloud.com/repository/prod.teamcity-central.afr</url>
        </pluginRepository>
    </pluginRepositories>

    <build>
        <sourceDirectory>${basedir}</sourceDirectory>
        <plugins>
            <plugin>
                <artifactId>kotlin-maven-plugin</artifactId>
                <groupId>org.jetbrains.kotlin</groupId>
                <version>${kotlin.version}</version>

                <configuration>
                    <jvmTarget>1.8</jvmTarget>
                </configuration>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jetbrains.teamcity</groupId>
                <artifactId>teamcity-configs-maven-plugin</artifactId>
                <version>${teamcity.dsl.version}</version>
                <configuration>
                    <format>kotlin</format>
                    <dstDir>target/generated-configs</dstDir>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>org.jetbrains.teamcity</groupId>
            <artifactId>configs-dsl-kotlin-latest</artifactId>
            <version>${teamcity.dsl.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.teamcity</groupId>
            <artifactId>configs-dsl-kotlin-plugins-latest</artifactId>
            <version>1.0-SNAPSHOT</version>
            <type>pom</type>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk8</artifactId>
            <version>${kotlin.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-script-runtime</artifactId>
            <version>${kotlin.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
            <version>${kotlin.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>co.a5labs.cloudops.ci-cd</groupId>
            <artifactId>pipeline-common</artifactId>
            <version>0.28.10-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test</artifactId>
            <version>${kotlin.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-serialization-json</artifactId>
            <version>${serialization.version}</version>
        </dependency>
        <dependency>
            <groupId>io.pebbletemplates</groupId>
            <artifactId>pebble</artifactId>
            <version>3.1.5</version>
        </dependency>
    </dependencies>
</project>