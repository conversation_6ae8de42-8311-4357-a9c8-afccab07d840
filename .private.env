PORT=3000
WORKER_CONCURRENCY=64
REDIS_HOST=localhost
# REDIS_HOST=fg-dev-manager-db.yzy1lq.clustercfg.apse1.cache.amazonaws.com # DEV, no creds, VPN required
REDIS_PORT=6379
REDIS_CLUSTER_MODE=false
# MONGO_HOST=localhost
# MONGO_USER=test
# MONGO_PASSWORD=test
MONGO_HOST=fg-dev-documentdb-elastic-cluster-575108924998.ap-southeast-1.docdb-elastic.amazonaws.com
MONGO_USER=dbadmin
MONGO_PASSWORD=WRHe1XnllMUFqyOgTMO9
ROBOT_CONFIG={"wpkHttpURL": "http://**************/wepoker", "platform" : "dev"}
ROBOT_CONFIG_WPK_URL="http://**************:83/wepoker"
ROBOT_CONFIG_WPK_PLATFORM=dev
WPTGO_URL=https://api.stg.wptg.a5-labs-cloud.com
WPTGO_WS_URL=wss://wptg-gate-stg.a5labsapp.co
WPTGO_APP_BUNDLE_ID=com.wptasia.wpt
# If you don't want to output some logging tags - enumerate them separated by ,
# NO_LOG_TAGS="NO_LOG_TAGS=JOB_PROGRESS, STRATEGY_SERVICE_CALL"
STRATEGY_SERVICE_URL="http://gtoglue.dev.aceguardian.io" # DEV
# STRATEGY_SERVICE_URL=http://gto-glue-svc.priv.stg.wptg.a5-labs-cloud.com # STAGING
MTT_CONFIG_BLWORLD=ws://*************:3001
MTT_CONFIG_BLAPI=http://*************:22001
MTT_CONFIG_MTTWORLD=ws://*************:3001
MTT_CONFIG_MTTGAME=ws://*************:4001
MTT_CONFIG_MTTAPI=http://*************:22001
# UNLEASH_API_URL=https://unleash.dev.fungamer.io/api
# UNLEASH_API_KEY=default:development.f01957ac1dc0b92ba4ec60e12cefb0d1c68f1e7ce308598fc6452f3b
