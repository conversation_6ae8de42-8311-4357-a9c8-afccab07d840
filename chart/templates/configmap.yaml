{{- $configmap := .Values.configmap | default dict }}
{{- $annotations := $configmap.annotations | default dict }}
{{- $labels := $configmap.labels | default dict }}
{{- $context := dict "name" $configmap.name "annotations" $annotations "labels" $labels }}
apiVersion: v1
kind: ConfigMap
{{- include "library-metadata" (dict "root" $ "context" $context ) }}
data:
  # GLUE_SERVICE_URL: "http://gto-glue-svc.aceguardian-ns:5000"
  # STRATEGY_SERVICE_URL: "http://gto-glue-svc.aceguardian-ns:5000"
  MANAGER_URL: "http://botmanager.bots.svc.cluster.local:8888/"
  NODE_ENV: {{ .Values.environment }}
  ENV: {{ .Values.environment }}
  REDIS_PORT: "6379"
  REDIS_CLUSTER_MODE: "true"
  STRATEGY_SERVICE_URL: "{{ .Values.configmap.STRATEGY_SERVICE_URL }}"
  # WPK Data
  ROBOT_CONFIG: {{ .Values.configmap.ROBOT_CONFIG | quote }}
  ROBOT_CONFIG_WPK_URL: {{ .Values.configmap.ROBOT_CONFIG_WPK_URL }}
  ROBOT_CONFIG_WPK_PLATFORM: {{ .Values.configmap.ROBOT_CONFIG_WPK_PLATFORM }}
  # WPTGO Data
  WPTGO_URL: {{ .Values.configmap.WPTGO_URL }}
  WPTGO_WS_URL: {{ .Values.configmap.WPTGO_WS_URL }}
  WPTGO_APP_BUNDLE_ID: "com.wptasia.wpt"
# MTT Data
  MTT_CONFIG_MTTWORLD: {{ .Values.configmap.MTT_CONFIG_MTTWORLD }}
  MTT_CONFIG_MTTGAME: {{ .Values.configmap.MTT_CONFIG_MTTGAME }}
  MTT_CONFIG_MTTAPI: {{ .Values.configmap.MTT_CONFIG_MTTAPI }}
  MTT_PROTOBUF_VERSION: {{ .Values.configmap.MTT_PROTOBUF_VERSION }}
  UNLEASH_API_URL: {{ .Values.configmap.UNLEASH_API_URL }}