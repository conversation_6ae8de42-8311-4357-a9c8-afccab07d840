environment: &environment prod

service:
  - type: ClusterIP
    port: 3000
    targetPort: 3000

configmap:
  STRATEGY_SERVICE_URL: https://gto-glue-svc.prod.kashxa-infra.com
  ROBOT_CONFIG: '{"wpkHttpURL": "https://game.wpk000.com:11111/wepoker", "platform" : "prd"}'
  ROBOT_CONFIG_WPK_URL: "https://game.wpk000.com:11111/wepoker"
  ROBOT_CONFIG_WPK_PLATFORM: "prd"
  WPTGO_URL: "https://api.wptglobal.com"
  WPTGO_WS_URL: "wss://gate-sec.wptglobal.com"
  MTT_CONFIG_URL: "https://92mtt-front.oss-accelerate.aliyuncs.com/mtt/appfile/url_config"
  MTT_CONFIG_TTL_SECONDS: 28800
  MTT_CONFIG_MTTWORLD: "wss://92smtp.omq2uk.com:3011"
  MTT_CONFIG_MTTGAME: "wss://92smtp.omq2uk.com:4011"
  MTT_CONFIG_MTTAPI: "http://mttapi.lihail.com"
  MTT_PROTOBUF_VERSION: "v2"
  UNLEASH_API_URL: "https://unleashedge.fungamer.io/api"

deployment:
  image:
    repository: 124355651851.dkr.ecr.ap-southeast-1.amazonaws.com/fg/botworker
    tag: 0.28.16
  terminationGracePeriodSeconds: "28800"
  revisionHistoryLimitCount: 5
  nodePoolTolerationValue: resource-intensive
  resources:
    limits:
      cpu: 16
      memory: "32768Mi"
    requests:
      cpu: 13
      memory: "26696Mi"
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "3000"
    prometheus.io/path: "/metrics"

secrets:
  - type: ESO
    name: documentdb-endpoint
    secrets:
      MONGO_HOST: data_storage/documentdb.elastic.endpoint
  - type: ESO
    name: redis-endpoint
    secrets:
      REDIS_HOST: data_storage/elasticache.manager.endpoint
  - type: ESO
    name: mongo-user
    secrets:
      MONGO_USER:
        key: data_storage/documentdb.elastic.credentials
        property: username
  - type: ESO
    name: mongo-password
    secrets:
      MONGO_PASSWORD:
        key: data_storage/documentdb.elastic.credentials
        property: password
  - type: ESO
    name: strategy-service-token
    secrets:
      STRATEGY_SERVICE_TOKEN: bots/svc.botworker.strategy_service_token
  - type: ESO
    name: secret-key
    secrets:
      SECRET_KEY: bots/svc.botworker.secret_key
  - type: ESO
    name: launchdarkly-sdk-key
    secrets:
      LAUNCHDARKLY_SDK_KEY: bots/launchdarkly.sdk_key
  - type: ESO
    name: unleash-api-token
    secrets:
      UNLEASH_API_TOKEN: management/unleash.edge.api_token

ingress:
  - ingressClassName: nginx
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
    rules:
      - host: botworker.fungamer.io
        paths:
          - path: /dashboard
            pathType: Prefix
            number: 3000
          - path: /arena
            pathType: Prefix
            number: 3000

hpa:
  - name: botworker
    targetDeployment:
      apiVersion: apps/v1
      kind: Deployment
      name: botworker
    minReplicas: 5
    maxReplicas: 50
    metrics:
      - type: Pods
        name: bullmq_node_utilization
        averageValue: "0.7"
    behavior:
      scaleUp:
        stabilizationWindowSeconds: 60
        selectPolicy: Max
        policies:
          - type: Pods
            value: 3
            periodSeconds: 15
      scaleDown:
        stabilizationWindowSeconds: 600
        selectPolicy: Min
        policies:
          - type: Pods
            value: 1
            periodSeconds: 60
